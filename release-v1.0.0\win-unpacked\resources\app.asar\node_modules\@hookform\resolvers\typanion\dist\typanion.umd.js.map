{"version": 3, "file": "typanion.umd.js", "sources": ["../src/typanion.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { FieldError, FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrors = (errors: string[], parsedErrors: FieldErrors = {}) => {\n  return errors.reduce((acc, error) => {\n    const fieldIndex = error.indexOf(':');\n\n    const field = error.slice(1, fieldIndex);\n    const message = error.slice(fieldIndex + 1).trim();\n\n    acc[field] = {\n      message,\n    } as FieldError;\n\n    return acc;\n  }, parsedErrors);\n};\n\nexport const typanionResolver: Resolver =\n  (validator, validatorOptions = {}) =>\n  (values, _, options) => {\n    const rawErrors: string[] = [];\n    const isValid = validator(\n      values,\n      Object.assign(\n        {},\n        {\n          errors: rawErrors,\n        },\n        validatorOptions,\n      ),\n    );\n    const parsedErrors = parseErrors(rawErrors);\n\n    if (isValid) {\n      options.shouldUseNativeValidation &&\n        validateFieldsNatively(parsedErrors, options);\n\n      return { values, errors: {} };\n    }\n\n    return { values: {}, errors: toNestErrors(parsedErrors, options) };\n  };\n"], "names": ["validator", "validatorOptions", "values", "_", "options", "rawErrors", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "errors", "parsedErrors", "reduce", "acc", "error", "fieldIndex", "indexOf", "field", "slice", "message", "trim", "parseErrors", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors"], "mappings": "iVAoBE,SAACA,EAAWC,eAAgB,IAAhBA,IAAAA,EAAmB,CAAE,GAChCC,SAAAA,EAAQC,EAAGC,GACV,IAAMC,EAAsB,GACtBC,EAAUN,EACdE,EACAK,OAAOC,OACL,GACA,CACEC,OAAQJ,GAEVJ,IAGES,EA7BU,SAACD,EAAkBC,GACrC,YADqCA,IAAAA,IAAAA,EAA4B,CAAE,GAC5DD,EAAOE,OAAO,SAACC,EAAKC,GACzB,IAAMC,EAAaD,EAAME,QAAQ,KAE3BC,EAAQH,EAAMI,MAAM,EAAGH,GACvBI,EAAUL,EAAMI,MAAMH,EAAa,GAAGK,OAM5C,OAJAP,EAAII,GAAS,CACXE,QAAAA,GAGKN,CACT,EAAGF,EACL,CAgByBU,CAAYf,GAEjC,OAAIC,GACFF,EAAQiB,2BACNC,yBAAuBZ,EAAcN,GAEhC,CAAEF,OAAAA,EAAQO,OAAQ,CAAA,IAGpB,CAAEP,OAAQ,GAAIO,OAAQc,EAAYA,aAACb,EAAcN,GAC1D,CAAC"}