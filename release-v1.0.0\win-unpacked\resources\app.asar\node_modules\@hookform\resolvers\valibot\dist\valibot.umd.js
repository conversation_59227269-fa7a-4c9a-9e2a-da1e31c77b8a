!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports,require("@hookform/resolvers"),require("react-hook-form"),require("valibot")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","react-hook-form","valibot"],o):o((e||self).hookformResolversValibot={},e.hookformResolvers,e.ReactHookForm,e.valibot)}(this,function(e,o,r,s){e.valibotResolver=function(e,t,i){return void 0===i&&(i={}),function(a,n,f){try{var u=!f.shouldUseNativeValidation&&"all"===f.criteriaMode;return Promise.resolve(s.safeParseAsync(e,a,Object.assign({},t,{abortPipeEarly:!u}))).then(function(e){if(e.issues){for(var t={};e.issues.length;){var n=e.issues[0],l=s.getDotPath(n);if(l&&(t[l]||(t[l]={message:n.message,type:n.type}),u)){var c=t[l].types,v=c&&c[n.type];t[l]=r.appendErrors(l,u,t,n.type,v?[].concat(v,n.message):n.message)}e.issues.shift()}return{values:{},errors:o.toNestErrors(t,f)}}return{values:i.raw?a:e.output,errors:{}}})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=valibot.umd.js.map
