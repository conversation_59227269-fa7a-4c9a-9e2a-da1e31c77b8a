"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const puppeteer_extra_1 = __importDefault(require("puppeteer-extra"));
const puppeteer_extra_plugin_stealth_1 = __importDefault(require("puppeteer-extra-plugin-stealth"));
// 设置正确的编码，解决中文显示问题
process.env.LANG = 'zh_CN.UTF-8';
process.env.LC_ALL = 'zh_CN.UTF-8';
process.env.LC_CTYPE = 'zh_CN.UTF-8';
// 在Windows平台下，设置控制台代码页为UTF-8
if (process.platform === 'win32') {
    try {
        const { execSync } = require('child_process');
        execSync('chcp 65001', { stdio: 'ignore' });
    }
    catch (err) {
        console.error('设置控制台代码页失败:', err);
    }
}
// 确保可以正确处理中文字符
function ensureUtf8(str) {
    try {
        // 如果已经是有效的UTF-8字符串，直接返回
        if (Buffer.from(str).toString('utf8') === str) {
            return str;
        }
        // 尝试解码可能的非UTF-8编码
        const buffer = Buffer.from(str, 'binary');
        return buffer.toString('utf8');
    }
    catch (e) {
        console.error('字符串编码转换失败:', e);
        return str; // 失败时返回原始字符串
    }
}
// 使用StealthPlugin插件来防止被网站的反爬虫机制检测到
puppeteer_extra_1.default.use((0, puppeteer_extra_plugin_stealth_1.default)());
console.log('--- 开始执行详情页下载选项抓取脚本 (v9 - 增强版) ---');
// 获取Chromium可执行文件路径
function getChromiumPath() {
    const path = require('path');
    const fs = require('fs');
    // 检查是否在打包环境中
    if (process.env.NODE_ENV === 'production' || process.pkg || process.resourcesPath) {
        console.log('[Detail Scraper] 检测到打包环境，查找内置Chromium');
        // 打包环境中的Chromium路径
        const possiblePaths = [
            path.join(process.resourcesPath, 'chromium', 'chrome.exe'),
            path.join(process.resourcesPath, '..', 'chromium', 'chrome.exe'),
            path.join(__dirname, '..', '..', 'chromium', 'chrome.exe'),
            path.join(process.cwd(), 'resources', 'chromium', 'chrome.exe')
        ];
        for (const chromiumPath of possiblePaths) {
            console.log(`[Detail Scraper] 检查Chromium路径: ${chromiumPath}`);
            if (fs.existsSync(chromiumPath)) {
                console.log(`[Detail Scraper] ✅ 找到Chromium: ${chromiumPath}`);
                return chromiumPath;
            }
        }
        console.error('[Detail Scraper] ❌ 未找到内置Chromium，将使用系统默认浏览器');
    }
    return undefined; // 使用Puppeteer默认的Chromium
}
// 使用Puppeteer从详情页面提取下载选项的函数
async function extractDownloadOptions(url) {
    console.log(`[Detail Scraper] 开始获取游戏 ${url} 的下载选项`);
    // 获取Chromium路径
    const chromiumPath = getChromiumPath();
    // 优化浏览器启动选项，减少内存使用
    const launchOptions = {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage', // 减少/dev/shm使用，避免内存问题
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--js-flags=--max-old-space-size=512' // 限制JS堆大小
        ],
        ignoreDefaultArgs: ['--enable-automation'],
        defaultViewport: { width: 1280, height: 1024 }
    };
    // 如果找到了Chromium路径，则使用它
    if (chromiumPath) {
        launchOptions.executablePath = chromiumPath;
        console.log(`[Detail Scraper] 使用Chromium路径: ${chromiumPath}`);
    }
    const browser = await puppeteer_extra_1.default.launch(launchOptions);
    const page = await browser.newPage();
    try {
        // 设置请求拦截，阻止不必要的资源加载
        await page.setRequestInterception(true);
        page.on('request', (req) => {
            const resourceType = req.resourceType();
            if (resourceType === 'image' ||
                resourceType === 'font' ||
                resourceType === 'stylesheet' ||
                resourceType === 'media') {
                req.abort();
            }
            else {
                req.continue();
            }
        });
        // 设置更短的超时时间
        page.setDefaultNavigationTimeout(30000);
        console.log(`[Detail Scraper] 正在访问页面: ${url}`);
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
        // 等待页面加载完成
        await page.waitForSelector('body', { timeout: 10000 });
        console.log('[Detail Scraper] 页面已加载，开始查找下载链接');
        // 获取页面内容，方便调试
        const htmlContent = await page.content();
        const shortHtml = htmlContent.substring(0, 5000) + '...'; // 只取前5000个字符
        console.log(`[Detail Scraper] 页面HTML片段：${shortHtml}`);
        // 使用页面评估提取下载选项，多种方式尝试
        const options = await page.evaluate(() => {
            const results = [];
            // 函数：尝试从元素提取数字
            function extractNumber(text) {
                if (!text)
                    return 0;
                const match = text.replace(/,/g, '').match(/\d+/);
                return match ? parseInt(match[0], 10) : 0;
            }
            // 方法1：查找标准下载表格
            console.log('尝试方法1：查找标准下载表格');
            const downloadTables = document.querySelectorAll('.download-attachments .da-attachments-table');
            console.log(`找到 ${downloadTables.length} 个下载表格`);
            if (downloadTables && downloadTables.length > 0) {
                downloadTables.forEach(table => {
                    // 获取表格中的所有行（跳过表头）
                    const rows = table.querySelectorAll('tbody tr');
                    console.log(`表格中有 ${rows.length} 行`);
                    rows.forEach(row => {
                        // 跳过标题行或分类行
                        if (row.classList.contains('alt') && row.querySelector('td[colspan]')) {
                            return;
                        }
                        // 提取下载链接
                        const linkElement = row.querySelector('td.attachment-title a');
                        if (!linkElement)
                            return;
                        const fileName = linkElement.textContent?.trim() || '';
                        const downloadUrl = linkElement.getAttribute('href') || '';
                        // 如果没有有效的下载URL，跳过
                        if (!downloadUrl)
                            return;
                        // 提取日期、大小和下载次数
                        const dateElement = row.querySelector('td.attachment-date');
                        const sizeElement = row.querySelector('td.attachment-size');
                        const downloadsElement = row.querySelector('td.attachment-downloads');
                        const dateAdded = dateElement ? dateElement.textContent?.trim() || 'N/A' : 'N/A';
                        const fileSize = sizeElement ? sizeElement.textContent?.trim() || 'N/A' : 'N/A';
                        // 解析下载次数为数字
                        let downloads = 0;
                        if (downloadsElement) {
                            downloads = extractNumber(downloadsElement.textContent);
                        }
                        results.push({
                            fileName,
                            downloadUrl,
                            dateAdded,
                            fileSize,
                            downloads
                        });
                    });
                });
            }
            // 方法2：如果方法1没有找到结果，尝试查找任何下载链接
            console.log('尝试方法2：查找任何下载链接');
            if (results.length === 0) {
                // 查找典型的下载链接区域
                const downloadSections = document.querySelectorAll('.download-attachments, .downloads, [id*="download"]');
                console.log(`找到 ${downloadSections.length} 个下载区域`);
                downloadSections.forEach(section => {
                    const links = section.querySelectorAll('a[href*="download"], a[href*=".zip"], a[href*=".exe"], a[href*=".rar"]');
                    console.log(`下载区域内有 ${links.length} 个链接`);
                    links.forEach(link => {
                        const fileName = link.textContent?.trim() || '下载文件';
                        const downloadUrl = link.getAttribute('href') || '';
                        if (downloadUrl) {
                            results.push({
                                fileName,
                                downloadUrl,
                                dateAdded: 'N/A', // 无法确定日期
                                fileSize: 'N/A', // 无法确定大小
                                downloads: 0 // 无法确定下载次数
                            });
                        }
                    });
                });
            }
            // 方法3：终极方案 - 尝试查找页面上任何可能的下载链接
            console.log('尝试方法3：全页面搜索下载链接');
            if (results.length === 0) {
                // 查找包含下载相关关键词的链接
                const allLinks = document.querySelectorAll('a[href]');
                console.log(`页面上共有 ${allLinks.length} 个链接`);
                allLinks.forEach(link => {
                    const href = link.getAttribute('href') || '';
                    const text = link.textContent?.trim() || '';
                    // 判断是否可能是下载链接
                    if (href.includes('download') ||
                        href.includes('down') ||
                        href.includes('file') ||
                        href.endsWith('.zip') ||
                        href.endsWith('.rar') ||
                        href.endsWith('.exe') ||
                        text.toLowerCase().includes('download') ||
                        text.toLowerCase().includes('下载')) {
                        const downloadUrl = href.startsWith('http') ? href : new URL(href, window.location.href).href;
                        // 检查是否已经添加过这个链接
                        if (!results.some(item => item.downloadUrl === downloadUrl)) {
                            // 尝试从链接文本或URL中提取文件名
                            let fileName = text || downloadUrl.split('/').pop() || '下载文件';
                            results.push({
                                fileName,
                                downloadUrl,
                                dateAdded: 'N/A',
                                fileSize: 'N/A',
                                downloads: 0
                            });
                        }
                    }
                });
            }
            return results;
        });
        console.log(`[Detail Scraper] 找到 ${options.length} 个下载链接`);
        // 如果有下载选项，打印第一个作为示例
        if (options.length > 0) {
            console.log(`[Detail Scraper] 示例下载选项: ${JSON.stringify(options[0])}`);
        }
        return options;
    }
    catch (error) {
        console.error('[Detail Scraper] 提取下载选项时出错:', error);
        return [];
    }
    finally {
        // 尝试保存页面截图，方便调试
        try {
            await page.screenshot({ path: 'detail-page-debug.png' });
            console.log('[Detail Scraper] 已保存页面截图用于调试');
        }
        catch (e) {
            console.error('[Detail Scraper] 保存截图失败:', e);
        }
        // 关闭页面和浏览器，释放内存
        await page.close();
        await browser.close();
        console.log('[Detail Scraper] 浏览器已关闭');
    }
}
// 主函数
async function main() {
    const trainerLink = process.argv[2];
    if (!trainerLink) {
        const errorMsg = '错误：未提供游戏链接参数';
        console.error(`[Detail Scraper] ${errorMsg}`);
        // 输出结果到stdout，兼容spawn和fork模式
        const result = { success: false, trainerLink: '', error: errorMsg };
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
        process.exit(1);
    }
    console.log(`[Detail Scraper] 开始获取游戏 ${trainerLink} 的下载选项`);
    try {
        const downloadOptions = await extractDownloadOptions(trainerLink);
        if (downloadOptions.length === 0) {
            console.warn('[Detail Scraper] 未找到任何下载选项');
            const result = {
                success: false,
                trainerLink,
                error: '未找到任何下载选项'
            };
            if (process.send) {
                process.send(result);
            }
            else {
                console.log('RESULT:' + JSON.stringify(result));
            }
        }
        else {
            console.log(`[Detail Scraper] 成功找到 ${downloadOptions.length} 个下载选项`);
            // 确保所有文本都是UTF-8编码
            const sanitizedOptions = downloadOptions.map(option => ({
                ...option,
                fileName: ensureUtf8(option.fileName),
                downloadUrl: ensureUtf8(option.downloadUrl),
                dateAdded: ensureUtf8(option.dateAdded),
                fileSize: ensureUtf8(option.fileSize)
            }));
            const result = {
                success: true,
                trainerLink: ensureUtf8(trainerLink),
                downloadOptions: sanitizedOptions
            };
            if (process.send) {
                process.send(result);
            }
            else {
                console.log('RESULT:' + JSON.stringify(result));
            }
        }
    }
    catch (error) {
        const errorMsg = error.message || '抓取下载选项时发生错误';
        console.error(`[Detail Scraper] 抓取过程中发生错误:`, errorMsg);
        const result = {
            success: false,
            trainerLink: ensureUtf8(trainerLink),
            error: ensureUtf8(errorMsg)
        };
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
    }
}
// 添加全局错误处理
process.on('uncaughtException', (error) => {
    console.error('[Detail Scraper] 未捕获的异常:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason) => {
    console.error('[Detail Scraper] 未处理的Promise拒绝:', reason);
});
// 设置超时，避免进程挂起
setTimeout(() => {
    console.error('[Detail Scraper] 脚本执行超时，强制退出');
    process.exit(1);
}, 60000); // 1分钟超时
// 执行主函数
main();
