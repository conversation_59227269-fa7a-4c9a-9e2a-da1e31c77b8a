import React, { useMemo, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  RefreshCcw, 
  Download, 
  Clock, 
  FileText, 
  Globe, 
  Package, 
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Search,
  Languages,
  HelpCircle
} from "lucide-react";
import { TrainerCard } from "./trainer-card";
import { useTrainerData, TrainerData } from "../../services/trainer-data.service";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";

interface TrainerListProps {}

export function TrainerList({}: TrainerListProps) {
  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [searchResults, setSearchResults] = useState<TrainerData[]>([]);
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const { toast } = useToast();

  const { 
    trainerData, 
    isLoading, 
    lastUpdated, 
    error, 
    refreshData, 
    pagination,
    nextPage: nextPageService,
    prevPage: prevPageService,
    changePage
  } = useTrainerData();

  // 监听搜索结果
  useEffect(() => {
    const electron = (window as any).electron;
    if (electron && electron.ipcRenderer) {
      // 使用正确的ipcRenderer.on API
      const removeListener = electron.ipcRenderer.on('search-results-response', async (data: any) => {
        console.log('收到搜索结果:', data);
        
        // 添加2秒延迟，确保页面内容完全加载
        console.log('添加2秒延迟处理搜索结果...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('延迟结束，开始处理搜索结果');
        
        setIsSearching(false);
        
        try {
          if (data.success) {
            // 适应后端返回的数据格式
            const results = data.results || data.data || [];
            console.log(`处理搜索结果: ${results.length} 条数据`);
            
            // 确保结果是数组
            if (Array.isArray(results)) {
              setSearchResults(results);
              
              // 将搜索结果保存到localStorage中以防止刷新后丢失
              try {
                localStorage.setItem('lastSearchQuery', searchQuery);
                localStorage.setItem('lastSearchResults', JSON.stringify(results));
                localStorage.setItem('lastSearchTime', new Date().toISOString());
              } catch (err) {
                console.error('保存搜索结果到localStorage失败:', err);
              }
              
              if (results.length === 0) {
                toast({
                  title: "未找到结果",
                  description: `没有找到与"${data.query || searchQuery}"相关的修改器`,
                  variant: "destructive"
                });
              } else {
                toast({
                  title: "搜索完成",
                  description: `找到 ${results.length} 个与"${data.query || searchQuery}"相关的修改器`,
                });
              }
            } else {
              console.error('搜索结果格式错误:', results);
              setSearchResults([]);
              toast({
                title: "搜索失败",
                description: "搜索结果格式错误",
                variant: "destructive"
              });
            }
          } else {
            console.error('搜索失败:', data.error || '未知错误');
            toast({
              title: "搜索失败",
              description: data.error || "搜索过程中出现错误",
              variant: "destructive"
            });
            setSearchResults([]);
          }
        } catch (error) {
          console.error('处理搜索结果时出错:', error);
          toast({
            title: "搜索失败",
            description: "处理搜索结果时出错",
            variant: "destructive"
          });
          setSearchResults([]);
        }
      });
      
      return () => {
        // 清理监听器
        if (removeListener) {
          removeListener();
        }
      };
    }
    
    return () => {};
  }, [searchQuery, toast]);

  // 从localStorage恢复搜索结果
  useEffect(() => {
    try {
      // 清除之前的搜索结果，确保应用打开时显示主页
      localStorage.removeItem('lastSearchQuery');
      localStorage.removeItem('lastSearchResults');
      localStorage.removeItem('lastSearchTime');
      
      // 不再从localStorage恢复搜索结果
      /*
      const savedQuery = localStorage.getItem('lastSearchQuery');
      const savedResults = localStorage.getItem('lastSearchResults');
      const savedTime = localStorage.getItem('lastSearchTime');
      
      if (savedQuery && savedResults && savedTime) {
        // 检查搜索结果是否在24小时内
        const lastSearchTime = new Date(savedTime).getTime();
        const now = new Date().getTime();
        const hoursDiff = (now - lastSearchTime) / (1000 * 60 * 60);
        
        if (hoursDiff < 24) {
          console.log('从本地存储恢复搜索结果');
          setSearchQuery(savedQuery);
          setSearchResults(JSON.parse(savedResults));
        } else {
          // 搜索结果过期，清除
          localStorage.removeItem('lastSearchQuery');
          localStorage.removeItem('lastSearchResults');
          localStorage.removeItem('lastSearchTime');
        }
      }
      */
    } catch (err) {
      console.error('处理本地存储失败:', err);
    }
  }, []);

  // 处理搜索函数
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    // 设置搜索状态
    setIsSearching(true);
    setSearchResults([]);
    
    console.log('发送搜索请求:', searchQuery, '时间:', new Date().toLocaleTimeString());
    const electron = (window as any).electron;
    
    if (electron && electron.ipcRenderer) {
      console.log('electron IPC可用，开始处理搜索请求');
      
      // 检查是否需要翻译（包含中文字符）
      const hasChinese = /[\u4e00-\u9fa5]/.test(searchQuery);
      let finalSearchQuery = searchQuery;
      
      if (hasChinese) {
        try {
          setIsTranslating(true);
          console.log('检测到中文游戏名称，尝试翻译:', searchQuery);
          
          // 显示翻译中的提示
          toast({
            title: "翻译中",
            description: `正在将"${searchQuery}"翻译为英文游戏名称...`,
            duration: 3000
          });
          
          // 调用翻译服务
          const translationResult = await electron.ipcRenderer.invoke('translate-game-name', searchQuery);
          console.log('翻译结果:', translationResult);
          
          if (translationResult && translationResult.success && translationResult.translatedName) {
            finalSearchQuery = translationResult.translatedName;
            console.log('使用翻译后的英文名称搜索:', finalSearchQuery);

            toast({
              title: "翻译成功",
              description: `已将"${searchQuery}"翻译为"${finalSearchQuery}"`,
              duration: 5000
            });
            
            // 添加延迟，确保页面更新完成
            await new Promise(resolve => setTimeout(resolve, 2000));
            console.log('翻译结果展示延迟完成，继续搜索流程');
          } else {
            console.log('翻译失败或未找到英文名称，使用原始查询');
            
            toast({
              title: "翻译未成功",
              description: `未能找到"${searchQuery}"的英文名称，将使用原始名称搜索`,
              variant: "destructive",
              duration: 5000
            });
            
            if (translationResult && translationResult.error) {
              console.error('翻译错误:', translationResult.error);
            }
          }
        } catch (error) {
          console.error('翻译过程中出错:', error);
          toast({
            title: "翻译出错",
            description: `翻译过程中出现错误，将使用原始名称搜索`,
            variant: "destructive",
            duration: 5000
          });
        } finally {
          setIsTranslating(false);
        }
      }
      
      // 显示搜索开始提示
      toast({
        title: "搜索开始",
        description: `正在搜索"${finalSearchQuery}"相关的修改器...`,
        duration: 3000
      });
      
      // 发送搜索请求
      console.log('发送最终搜索请求:', finalSearchQuery);
      electron.ipcRenderer.send('search-trainers', finalSearchQuery);
      
      // 添加调试日志
      console.log(`已发送搜索请求: "${finalSearchQuery}"`);
      
      // 设置超时，如果30秒内没有收到响应，则显示超时提示
      setTimeout(() => {
        if (isSearching) {
          setIsSearching(false);
          toast({
            title: "搜索超时",
            description: "搜索请求超时，请稍后重试",
            variant: "destructive"
          });
        }
      }, 30000);
    } else {
      console.error('无法发送搜索请求，electron.ipcRenderer不可用');
      setIsSearching(false);
      
      toast({
        title: "搜索失败",
        description: "无法发送搜索请求，请检查应用配置",
        variant: "destructive"
      });
    }
  };

  // 处理回车键搜索
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 清除搜索结果
  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
    
    // 清除本地存储的搜索结果
    try {
      localStorage.removeItem('lastSearchQuery');
      localStorage.removeItem('lastSearchResults');
      localStorage.removeItem('lastSearchTime');
    } catch (err) {
      console.error('清除本地存储的搜索结果失败:', err);
    }
  };

  // 根据搜索关键词过滤本地数据
  const filteredTrainers = useMemo(() => {
    // 如果有搜索结果，优先显示搜索结果，不受trainerData更新的影响
    if (searchResults.length > 0) {
      console.log('使用搜索结果显示数据:', searchResults.length, '条');
      return searchResults;
    }

    // 否则过滤本地数据
    if (!searchQuery.trim() || isSearching) return trainerData;
    const query = searchQuery.toLowerCase();
    return trainerData.filter(
      (trainer) =>
        trainer.title.toLowerCase().includes(query) ||
        trainer.link.toLowerCase().includes(query)
    );
  }, [searchQuery, trainerData, searchResults, isSearching]);

  // 获取当前显示的数据
  const displayedTrainers = useMemo(() => {
    // 如果有搜索结果，始终优先显示搜索结果
    if (searchResults.length > 0) {
      return searchResults;
    }
    return filteredTrainers;
  }, [searchResults, filteredTrainers]);

  // 打开官方网站
  const openOfficialWebsite = () => {
    console.log('点击了访问官方网站按钮');
    const electron = (window as any).electron;
    console.log('electron对象是否存在:', !!electron);
    
    if (electron && electron.ipcRenderer) {
      console.log('electron.ipcRenderer是否存在:', !!electron.ipcRenderer);
      console.log('发送打开官方网站请求...');
      electron.ipcRenderer.send('open-external-url', 'https://flingtrainer.com/');
      console.log('已发送打开官方网站请求');
    } else {
      console.error('electron.ipcRenderer不存在，无法发送打开网站请求');
      
      // 使用原生方式尝试打开
      try {
        window.open('https://flingtrainer.com/', '_blank');
        console.log('尝试使用window.open打开网站');
      } catch (error) {
        console.error('window.open失败:', error);
      }
    }
  };

  // 处理分页函数
  const handleNextPage = () => {
    // 如果有搜索结果，不执行分页操作
    if (searchResults.length > 0) {
      console.log('当前有搜索结果，不执行分页操作');
      return;
    }
    
    console.log('点击下一页按钮');
    console.log('当前分页状态:', pagination);
    if (pagination && pagination.hasNextPage) {
      console.log(`尝试加载下一页: ${pagination.currentPage + 1}`);
      nextPageService();
    } else {
      console.log('没有下一页或分页信息不完整');
    }
  };

  const handlePrevPage = () => {
    // 如果有搜索结果，不执行分页操作
    if (searchResults.length > 0) {
      console.log('当前有搜索结果，不执行分页操作');
      return;
    }
    
    console.log('点击上一页按钮');
    console.log('当前分页状态:', pagination);
    if (pagination && pagination.hasPrevPage) {
      console.log(`尝试加载上一页: ${pagination.currentPage - 1}`);
      prevPageService();
    } else {
      console.log('没有上一页或分页信息不完整');
    }
  };

  return (
    <div className="flex-1 overflow-auto bg-slate-900">
      {/* 数据源展示区域 */}
      <div className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Globe className="w-5 h-5 text-blue-400" />
                <h3 className="text-white font-semibold">风灵月影修改器官方网站：FLiNG Trainer</h3>
              </div>
              <p className="text-slate-300 text-sm">实时同步最新修改器数据，确保版本兼容性和功能完整性</p>
              <div className="flex items-center gap-4 mt-2 text-xs text-slate-400">
                <span className="flex items-center gap-1">
                  <Package className="w-3 h-3" />
                  修改器总数: {trainerData.length}
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  最后更新: {lastUpdated}
                </span>
                <button 
                  onClick={() => refreshData()}
                  className="flex items-center gap-1 text-blue-400 hover:text-blue-300 transition-colors"
                  disabled={isLoading || isSearching}
                >
                  <RefreshCcw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
                  {isLoading ? '同步中...' : '立即更新'}
                </button>
              </div>
            </div>
            <Button
              variant="default"
              size="sm"
              onClick={openOfficialWebsite}
              className="bg-blue-600 hover:bg-blue-500 text-white font-medium h-10"
            >
              <Globe className="w-4 h-4 mr-2" />
              访问官方网站
            </Button>
          </div>
        </div>
      </div>

      {/* 搜索栏 */}
      <div className="bg-slate-800 p-4">
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setShowHelpDialog(true)}
            className="text-white flex items-center gap-1 px-3"
            style={{ backgroundColor: 'rgb(220 38 38)' }}
            title="搜索帮助"
          >
            <HelpCircle className="w-4 h-4" />
            <span>搜索帮助</span>
          </Button>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input
              placeholder="搜索修改器、游戏名称...（支持中文自动翻译）"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <Button 
            variant="default"
            className="bg-blue-600 hover:bg-blue-500 text-white"
            onClick={handleSearch}
            disabled={isSearching || isTranslating || !searchQuery.trim()}
          >
            {isSearching || isTranslating ? (
              <RefreshCcw className="w-4 h-4 mr-1 animate-spin" />
            ) : (
              <>
                {/[\u4e00-\u9fa5]/.test(searchQuery) ? (
                  <Languages className="w-4 h-4 mr-1" />
                ) : (
                  <Search className="w-4 h-4 mr-1" />
                )}
              </>
            )}
            {isTranslating ? '翻译中...' : isSearching ? '搜索中...' : '搜索'}
          </Button>
          {searchResults.length > 0 && (
            <Button
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
              onClick={clearSearch}
            >
              清除结果
            </Button>
          )}
        </div>
      </div>

      {/* 顶部工具栏 */}
      <div className="sticky top-0 bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 p-4 z-10">
        <div className="flex items-center justify-between">
          <div className="text-white">
            {searchResults.length > 0 ? (
              <span className="font-medium">搜索结果: {searchResults.length} 个修改器</span>
            ) : (
              <span className="font-medium">找到 {filteredTrainers.length} 个修改器</span>
            )}
            {searchQuery && <span className="text-slate-400 ml-2">搜索: "{searchQuery}"</span>}
            {!isSearching && !searchResults.length && pagination && pagination.currentPage > 1 && (
              <span className="text-slate-400 ml-2">第 {pagination.currentPage} 页</span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <button className="px-3 py-1.5 text-sm border border-slate-600 rounded-md text-slate-300 hover:bg-slate-800 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              最新更新
              <ChevronDown className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      {/* 修改器列表 */}
      <div className="p-4">
        {error && !isSearching && (
          <div className="bg-red-900/30 border border-red-600 text-red-300 p-4 rounded-md mb-4">
            <p>加载数据出错: {error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2 bg-red-800/30 border-red-600 text-red-300 hover:bg-red-800/50"
              onClick={() => refreshData()}
            >
              <RefreshCcw className="w-4 h-4 mr-2" />
              重试
            </Button>
          </div>
        )}

        {/* 搜索中状态 */}
        {(isSearching || isTranslating) && (
          <div className="text-center py-20">
            {isTranslating ? (
              <>
                <Languages className="w-12 h-12 mx-auto mb-4 text-blue-400 animate-pulse" />
                <p className="text-slate-300">正在翻译"{searchQuery}"...</p>
              </>
            ) : (
              <>
                <Search className="w-12 h-12 mx-auto mb-4 text-blue-400 animate-pulse" />
                <p className="text-slate-300">正在搜索"{searchQuery}"相关的修改器...</p>
              </>
            )}
            <p className="text-sm text-slate-400 mt-2">这可能需要一些时间，请稍候...</p>
          </div>
        )}
        {/* 加载中状态 */}
        {!isSearching && isLoading && trainerData.length === 0 ? (
          <div className="text-center py-20">
            <RefreshCcw className="w-12 h-12 mx-auto mb-4 text-blue-400 animate-spin" />
            <p className="text-slate-300">正在加载修改器数据...</p>
          </div>
        ) : !isSearching && displayedTrainers.length === 0 ? (
          <div className="text-center text-slate-400 py-20">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>没有找到匹配的修改器</p>
            <p className="text-sm mt-2">尝试使用不同的关键词搜索</p>
          </div>
        ) : !isSearching && (
          <div className="space-y-3">
            {displayedTrainers.map((trainer, index) => (
              <TrainerCard 
                key={`${trainer.link}-${index}`} 
                trainer={trainer} 
                index={index} 
              />
            ))}
          </div>
        )}
        
        {/* 分页控制 - 只在非搜索结果模式下显示 */}
        {!isLoading && !isSearching && searchResults.length === 0 && filteredTrainers.length > 0 && pagination && (
          <div className="mt-6 flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevPage}
              disabled={!pagination.hasPrevPage || isLoading}
              className={`px-4 py-2 ${!pagination.hasPrevPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-800'}`}
            >
              {isLoading ? (
                <RefreshCcw className="w-4 h-4 mr-1 animate-spin" />
              ) : (
                <ChevronLeft className="w-4 h-4 mr-1" />
              )}
              上一页
            </Button>
            
            <div className="px-4 py-2 bg-slate-800 text-white rounded-md flex items-center">
              {isLoading ? (
                <div className="flex items-center">
                  <RefreshCcw className="w-4 h-4 mr-2 animate-spin" />
                  <span>加载中...</span>
                </div>
              ) : (
                <span>第 {pagination.currentPage} 页</span>
              )}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={!pagination.hasNextPage || isLoading}
              className={`px-4 py-2 ${!pagination.hasNextPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-800'}`}
            >
              {isLoading ? (
                <RefreshCcw className="w-4 h-4 mr-1 animate-spin" />
              ) : (
                <>
                  下一页
                  <ChevronRight className="w-4 h-4 ml-1" />
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* 搜索帮助弹窗 */}
      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="bg-slate-800 text-white border-slate-600 max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl text-white">搜索帮助</DialogTitle>
            <DialogDescription className="text-slate-300">
              如何找到您需要的游戏修改器
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 text-slate-300">
            <div className="space-y-2">
              <h3 className="font-medium text-white">搜索技巧</h3>
              <p>1. 请尝试使用游戏的<span className="text-blue-400">完整英文名称</span>进行搜索，这样能获得最准确的结果。</p>
              <p>2. 如果搜索不到，请点击<span className="text-blue-400">访问官方网站</span>按钮，在官方网站上搜索游戏。</p>
              <p>3. 如果官方网站也没有找到，可能是该游戏暂无修改器，或者是年代较久远的游戏。</p>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium text-white">关于本软件</h3>
              <p>本软件仅收录了<span className="text-yellow-400 font-medium">风灵月影（FLiNG）</span>制作的游戏修改器，不包含其他来源的修改器。</p>
              <p>风灵月影（FLiNG）是知名修改器制作者，专注于制作高质量的游戏修改器。</p>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium text-white">其他建议</h3>
              <p>如果您确实需要但未找到相关修改器，可以尝试其他渠道或工具进行查询。</p>
            </div>
          </div>
          
          <DialogFooter className="flex flex-row gap-2 justify-end">
            <Button 
              variant="default" 
              onClick={() => setShowHelpDialog(false)}
              style={{ backgroundColor: '#2563eb', color: 'white' }}
              className="hover:bg-blue-500"
            >
              我知道了
            </Button>
            <Button 
              variant="default" 
              onClick={openOfficialWebsite}
              style={{ backgroundColor: '#2563eb', color: 'white' }}
              className="hover:bg-blue-500"
            >
              <Globe className="w-4 h-4 mr-2" />
              访问官方网站
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 