import { app, BrowserWindow, ipcMain, shell } from 'electron';
import path from 'path';
import { DownloadManager } from './download-manager';
import fs from 'fs';
import puppeteer from 'puppeteer';
import { <PERSON>raperWorker } from './scraper-worker';
import { SubprocessManager } from './subprocess-manager';

// 强制设置控制台输出编码为UTF-8
if (process.platform === 'win32') {
  try {
    // 设置控制台代码页为UTF-8
    const { execSync } = require('child_process');
    execSync('chcp 65001', { stdio: 'inherit' });
    console.log('[Main Process] 已设置控制台代码页为UTF-8');
    
    // 强制设置Node.js进程编码
    process.env.LANG = 'zh_CN.UTF-8';
    process.env.LC_ALL = 'zh_CN.UTF-8';
    process.env.LC_CTYPE = 'zh_CN.UTF-8';
    
    // 设置stdout和stderr的编码
    if (process.stdout.setEncoding) {
      process.stdout.setEncoding('utf8');
    }
    if (process.stderr.setEncoding) {
      process.stderr.setEncoding('utf8');
    }
  } catch (err) {
    console.error('[Main Process] 设置控制台代码页失败:', err);
  }
} else {
  // 非Windows平台也设置编码
  process.env.LANG = 'zh_CN.UTF-8';
  process.env.LC_ALL = 'zh_CN.UTF-8';
  process.env.LC_CTYPE = 'zh_CN.UTF-8';
}

let mainWindow: BrowserWindow | null;
let downloadManager: DownloadManager | null = null;
let cachedData: any[] | null = null; // 用于缓存抓取到的数据
let rendererReady = false; // 标记渲染进程是否已准备好
let isScraperRunning = false; // 标记爬虫是否正在运行
let scraperWorker: ScraperWorker | null = null; // 新的爬虫工作器

// 确保文本正确编码
function ensureUtf8(str: string): string {
  try {
    // 如果已经是有效的UTF-8字符串，直接返回
    if (Buffer.from(str).toString('utf8') === str) {
      return str;
    }
    // 尝试解码可能的非UTF-8编码
    const buffer = Buffer.from(str, 'binary');
    return buffer.toString('utf8');
  } catch (e) {
    console.error('[Main Process] 字符串编码转换失败:', e);
    return str; // 失败时返回原始字符串
  }
}





// 动态获取Chromium可执行文件路径
function getChromiumExecutablePath(): string {
  if (app.isPackaged) {
    // 在打包应用中，Chromium 浏览器被打包到资源目录
    console.log(`[Main Process] 打包应用检测到`);
    console.log(`[Main Process] process.resourcesPath: ${process.resourcesPath}`);
    console.log(`[Main Process] app.getAppPath(): ${app.getAppPath()}`);
    console.log(`[Main Process] process.execPath: ${process.execPath}`);
    console.log(`[Main Process] __dirname: ${__dirname}`);

    // 尝试多个可能的路径
    const possiblePaths = [
      path.join(process.resourcesPath, 'chromium', 'chrome.exe'),
      path.join(app.getAppPath(), 'resources', 'chromium', 'chrome.exe'),
      path.join(path.dirname(process.execPath), 'resources', 'chromium', 'chrome.exe'),
      path.join(__dirname, '..', '..', 'chromium', 'chrome.exe'),
      path.join(__dirname, '..', '..', 'resources', 'chromium', 'chrome.exe')
    ];

    for (const executablePath of possiblePaths) {
      console.log(`[Main Process] 检查 Chromium 路径: ${executablePath}`);
      if (fs.existsSync(executablePath)) {
        console.log(`[Main Process] ✅ 找到 Chromium: ${executablePath}`);
        return executablePath;
      } else {
        console.log(`[Main Process] ❌ 路径不存在: ${executablePath}`);
      }
    }

    // 如果所有路径都不存在，记录详细信息并抛出错误
    console.error(`[Main Process] 严重错误: 无法找到打包的 Chromium 浏览器`);
    console.error(`[Main Process] 已检查的路径:`);
    possiblePaths.forEach(p => console.error(`  - ${p}`));

    // 列出资源目录的内容以便调试
    try {
      const resourcesContent = fs.readdirSync(process.resourcesPath);
      console.log(`[Main Process] 资源目录内容: ${resourcesContent.join(', ')}`);

      const chromiumPath = path.join(process.resourcesPath, 'chromium');
      if (fs.existsSync(chromiumPath)) {
        const chromiumContent = fs.readdirSync(chromiumPath);
        console.log(`[Main Process] Chromium 目录内容: ${chromiumContent.join(', ')}`);
      }
    } catch (err) {
      console.error(`[Main Process] 无法读取资源目录: ${err}`);
    }

    // 返回第一个路径作为默认值，即使它不存在
    return possiblePaths[0];
  } else {
    // 在开发模式中，Puppeteer 自动查找可执行文件
    const devPath = puppeteer.executablePath();
    console.log(`[Main Process] 开发模式。使用 Puppeteer 默认路径: ${devPath}`);
    return devPath;
  }
}

// 启动抓取器工作器，使用主进程内的爬虫
async function startScraperProcess(page: number = 1) {
  // 如果爬虫正在运行，不再重复启动
  if (isScraperRunning) {
    console.log('[Main Process] 爬虫进程正在运行，忽略此次请求');
    return;
  }

  // 设置运行状态
  isScraperRunning = true;

  try {
    console.log(`[Main Process] 开始启动爬虫工作器，页码: ${page}`);

    // 获取 Chromium 路径
    const chromiumPath = getChromiumExecutablePath();
    console.log(`[Main Process] 使用的Chromium路径: ${chromiumPath}`);

    // 验证 Chromium 路径是否存在
    if (!fs.existsSync(chromiumPath)) {
      throw new Error(`Chromium 可执行文件不存在: ${chromiumPath}`);
    }

    // 如果已存在工作器，先关闭
    if (scraperWorker) {
      console.log('[Main Process] 关闭现有爬虫工作器...');
      await scraperWorker.close();
      scraperWorker = null;
    }

    // 创建新的爬虫工作器
    scraperWorker = new ScraperWorker(chromiumPath);

    // 初始化浏览器
    await scraperWorker.initialize();

    // 开始抓取
    console.log(`[Main Process] 开始抓取第 ${page} 页数据...`);
    const result = await scraperWorker.scrapeTrainers(page);

    if (result.success && result.data) {
      console.log(`[Main Process] ✅ 抓取成功，获得 ${result.data.length} 条数据`);

      // 缓存数据
      cachedData = result.data;

      // 获取分页信息
      const paginationInfo = result.pagination || {
        currentPage: page,
        hasNextPage: true,
        hasPrevPage: page > 1,
        totalPages: 10
      };

      console.log('[Main Process] 分页信息:', JSON.stringify(paginationInfo));

      // 发送数据到渲染进程
      if (rendererReady && mainWindow) {
        mainWindow.webContents.send('update-trainer-data', cachedData, paginationInfo);
        console.log('[Main Process] 数据已发送到渲染进程');
      } else {
        console.log('[Main Process] 数据已缓存，等待渲染进程就绪...');
      }
    } else {
      throw new Error(result.error || '抓取失败');
    }

  } catch (error) {
    console.error('[Main Process] ❌ 爬虫工作器执行失败:', error);

    if (mainWindow) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      mainWindow.webContents.send('update-trainer-data-error', `加载数据出错，爬虫进程异常退出，代码：1`);
    }
  } finally {
    // 重置运行状态
    isScraperRunning = false;
  }
}







// 总是从文件加载，因为我们不再使用 dev server
function loadMainWindow() {
  if (!mainWindow || mainWindow.isDestroyed()) return;
  
  const indexPath = path.join(__dirname, '../renderer/index.html');
  console.log(`[Main Process] 加载渲染进程文件: ${indexPath}`);
  
  // 验证文件是否存在
  if (!fs.existsSync(indexPath)) {
    console.error(`[Main Process] 错误: 渲染文件不存在: ${indexPath}`);
    
    // 尝试查找可能的位置
    const possibleLocations = [
      path.join(app.getAppPath(), 'dist/renderer/index.html'),
      path.join(process.cwd(), 'dist/renderer/index.html')
    ];
    
    for (const loc of possibleLocations) {
      console.log(`[Main Process] 尝试备选位置: ${loc}`);
      if (fs.existsSync(loc)) {
        console.log(`[Main Process] 找到备选文件位置: ${loc}`);
        mainWindow.loadFile(loc).catch(err => {
          console.error('[Main Process] 加载渲染进程文件失败:', err);
        });
        return;
      }
    }
    
    // 如果还是找不到，显示错误信息
    mainWindow.loadURL(`data:text/html,<html><body><h1>错误：找不到界面文件</h1><p>请重新构建应用</p></body></html>`);
    return;
  }
  
  mainWindow.loadFile(indexPath).catch(err => {
    console.error('[Main Process] 加载渲染进程文件失败:', err);
  });
}

function createWindow() {
  // 检查是否已存在窗口，避免重复创建
  if (mainWindow && !mainWindow.isDestroyed()) {
    console.log('[Main Process] 窗口已存在，跳过创建');
    mainWindow.focus();
    return;
  }

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    frame: false,
    titleBarStyle: 'hidden', // 隐藏默认的标题栏
    // 完全禁用系统搜索框
    titleBarOverlay: false,
    fullscreenable: true,
    // 设置窗口性能参数
    show: false,
    backgroundColor: '#1a1a1a', // 深色背景，减少闪烁
    paintWhenInitiallyHidden: true,
    enableLargerThanScreen: false,
    // 窗口设置
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      // 安全配置
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: true,
      // 性能优化
      backgroundThrottling: false,
      // 减少不必要的功能以提高性能
      spellcheck: false,
      webgl: true,
      plugins: false,
      experimentalFeatures: false,
      autoplayPolicy: 'no-user-gesture-required' as const
    }
  });

  // 进一步优化窗口性能
  if (mainWindow.webContents) {
    mainWindow.webContents.backgroundThrottling = false;
    
    // 设置内容预热，提高首次渲染速度
    mainWindow.webContents.once('did-finish-load', () => {
      console.log('[Main Process] 窗口内容加载完成');
      
      // 预先进行一次渲染，避免首次渲染卡顿
      mainWindow?.webContents.setVisualZoomLevelLimits(1, 1);
    });
    
    // 优化窗口显示逻辑
    mainWindow.once('ready-to-show', () => {
      console.log('[Main Process] 窗口准备好显示');
      mainWindow?.show();
      mainWindow?.focus();
    });
    
    // 监听渲染进程崩溃事件，使用electron标准事件
    mainWindow.webContents.on('render-process-gone', (event, details) => {
      console.error(`[Main Process] 渲染进程异常: ${details.reason}`);
      
      // 尝试重载窗口
      if (mainWindow && !mainWindow.isDestroyed()) {
        console.log('[Main Process] 尝试重新加载窗口...');
        mainWindow.reload();
      }
    });
  }

  // 初始化下载管理器
  downloadManager = new DownloadManager(mainWindow);

  // 监听来自渲染进程的窗口控制事件
  ipcMain.on('window-minimize', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.minimize();
    }
  });
  
  ipcMain.on('window-maximize', () => {
    if (!mainWindow || mainWindow.isDestroyed()) return;
    
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });
  
  ipcMain.on('window-close', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.close();
    }
  });

  // 监听渲染进程的就绪信号
  ipcMain.on('renderer-ready', () => {
    console.log('[Main Process] 收到渲染进程就绪信号，时间:', new Date().toLocaleTimeString());
    rendererReady = true;
    
    // 如果已有缓存数据，立即发送
    if (cachedData && mainWindow && !mainWindow.isDestroyed()) {
      console.log(`[Main Process] 发送缓存数据 (${cachedData.length}项)...`);
      try {
        mainWindow.webContents.send('update-trainer-data', cachedData);
      } catch (err) {
        console.error('[Main Process] 发送缓存数据时出错:', err);
      }
    } else {
      console.log('[Main Process] 没有缓存数据可发送，启动爬虫获取数据');
      // 如果没有缓存数据且爬虫未运行，自动获取数据
      if (!isScraperRunning && !cachedData) {
        startScraperProcess();
      }
    }
  });

  // 添加搜索游戏的IPC处理器
  ipcMain.on('search-trainers', async (event, searchQuery) => {
    console.log(`[Main Process] 收到搜索请求: "${searchQuery}"`);

    // 确保搜索关键词是字符串并且非空
    if (!searchQuery || typeof searchQuery !== 'string' || searchQuery.trim() === '') {
      console.error('[Main Process] 搜索关键词无效:', searchQuery);
      if (mainWindow) {
        mainWindow.webContents.send('search-results-response', {
          success: false,
          results: [],
          error: '搜索关键词无效'
        });
      }
      return;
    }

    try {
      console.log(`[Main Process] 原始搜索关键词: "${searchQuery}"`);
      console.log(`[Main Process] 编码: ${Buffer.from(searchQuery).toString('hex')}`);

      const subprocessManager = SubprocessManager.getInstance();
      const result = await subprocessManager.startSearchScraper(searchQuery);
      console.log('[Main Process] 搜索完成:', result);

      if (mainWindow) {
        mainWindow.webContents.send('search-results-response', result);
      }
    } catch (error: any) {
      console.error('[Main Process] 搜索失败:', error);
      if (mainWindow) {
        mainWindow.webContents.send('search-results-response', {
          success: false,
          results: [],
          error: error.message || '搜索失败'
        });
      }
    }
  });

  // 监听渲染进程请求数据刷新
  ipcMain.on('request-trainer-data', (event, page = 1) => {
    console.log(`[Main Process] 收到数据刷新请求，页码: ${page}`);
    
    // 如果没有缓存数据或强制刷新，则启动爬虫
    if (!cachedData || page > 1) {
      console.log('[Main Process] 没有缓存数据或请求非首页，启动爬虫获取数据');
      startScraperProcess(page);
    } else {
      console.log('[Main Process] 使用缓存数据');
      // 使用缓存数据
      if (mainWindow) {
        // 创建分页信息
        const paginationInfo = {
          currentPage: 1,
          hasNextPage: true, // 确保首页总是有下一页
          hasPrevPage: false, // 第1页没有上一页
          totalPages: 10 // 假设有10页，实际情况可能需要动态计算
        };
        
        mainWindow.webContents.send('update-trainer-data', cachedData, paginationInfo);
        console.log('[Main Process] 缓存数据已发送到渲染进程');
      }
    }
  });



  // 监听下载文件请求
  ipcMain.on('download-file', (event, url, filename) => {
    // 确保文件名正确编码
    const sanitizedFilename = ensureUtf8(filename);
    console.log(`[Main Process] 收到下载文件请求: ${url}, 文件名: ${sanitizedFilename}`);
    if (downloadManager) {
      downloadManager.download(url, sanitizedFilename);
    }
  });

  // 监听获取下载列表请求
  ipcMain.on('get-downloads', (event) => {
    if (downloadManager) {
      const downloads = downloadManager.getAllDownloads();
      // 不需要在这里转换，已在download-manager中处理
      event.reply('downloads-list-update', downloads);
    }
  });

  // 监听清除已完成下载请求
  ipcMain.on('clear-completed-downloads', () => {
    if (downloadManager) {
      downloadManager.clearCompletedDownloads();
    }
  });

  // 监听删除单个下载请求
  ipcMain.on('delete-download', (event, downloadId) => {
    console.log(`[Main Process] 收到删除下载请求: ${downloadId}`);
    if (downloadManager) {
      const result = downloadManager.deleteDownload(downloadId);
      console.log(`[Main Process] 删除下载${result ? '成功' : '失败'}: ${downloadId}`);
    }
  });

  // 监听设置下载目录请求
  ipcMain.on('set-download-folder', (event, folder) => {
    if (downloadManager) {
      const sanitizedFolder = ensureUtf8(folder);
      downloadManager.setDownloadFolder(sanitizedFolder);
      event.reply('download-folder-updated', sanitizedFolder);
    }
  });

  // 监听获取下载目录请求
  ipcMain.on('get-download-folder', (event) => {
    if (downloadManager) {
      const folder = downloadManager.getDownloadFolder();
      const sanitizedFolder = ensureUtf8(folder);
      event.reply('download-folder', sanitizedFolder);
    }
  });

  // 添加监听打开外部链接的请求
  ipcMain.on('open-external-url', (event, url) => {
    console.log(`[Main Process] 收到打开外部链接请求: "${url}"`);
    
    // 白名单URL检查
    const validUrls = [
      'https://flingtrainer.com/',
      'https://flingtrainer.com',
      'https://www.baidu.com/' // 添加百度域名到白名单
    ];
    
    // 检查URL是否以白名单中的任一URL开头
    const isValidUrl = validUrls.some(validUrl => 
      url === validUrl || url.startsWith(`${validUrl}/`)
    );
    
    if (isValidUrl) {
      console.log(`[Main Process] URL在白名单中，准备打开: "${url}"`);
      
      shell.openExternal(url)
        .then(() => {
          console.log(`[Main Process] 成功打开链接: "${url}"`);
        })
        .catch((err) => {
          console.error(`[Main Process] 打开链接失败: ${err}`);
          
          // 如果常规方法失败，尝试其他方式打开
          try {
            const { exec } = require('child_process');
            if (process.platform === 'win32') {
              console.log('[Main Process] 尝试使用windows start命令打开URL');
              exec(`start ${url}`);
            } else if (process.platform === 'darwin') {
              console.log('[Main Process] 尝试使用macOS open命令打开URL');
              exec(`open ${url}`);
            } else {
              console.log('[Main Process] 尝试使用xdg-open命令打开URL');
              exec(`xdg-open ${url}`);
            }
          } catch (execErr) {
            console.error(`[Main Process] 尝试使用exec打开URL失败: ${execErr}`);
          }
        });
    } else {
      console.error(`[Main Process] 拒绝打开非白名单URL: "${url}"`);
    }
  });

  // 处理游戏名称翻译请求
  ipcMain.handle('translate-game-name', async (event, gameName: string) => {
    console.log('收到游戏名称翻译请求:', gameName);

    try {
      const subprocessManager = SubprocessManager.getInstance();
      const result = await subprocessManager.startTranslator(gameName);
      console.log('[Main Process] 翻译完成:', result);
      return result;
    } catch (error: any) {
      console.error('[Main Process] 翻译失败:', error);
      return {
        success: false,
        error: error.message || '翻译失败'
      };
    }
  });

  // 监听获取下载选项请求 (新的handle方式)
  ipcMain.handle('get-download-options', async (event, trainerLink) => {
    console.log(`[Main Process] 收到获取下载选项请求(handle): ${trainerLink}`);

    try {
      const subprocessManager = SubprocessManager.getInstance();
      const result = await subprocessManager.startDetailScraper(trainerLink);
      console.log('[Main Process] 详情抓取完成:', result);
      return result;
    } catch (error: any) {
      console.error('[Main Process] 详情抓取失败:', error);
      return {
        success: false,
        trainerLink,
        error: error.message || '获取下载选项失败'
      };
    }
  });

  // 监听获取下载选项请求 (兼容旧的send/on方式)
  ipcMain.on('request-trainer-download-options', async (event, trainerLink) => {
    console.log(`[Main Process] 🎯 收到获取下载选项请求: ${trainerLink}`);

    try {
      const subprocessManager = SubprocessManager.getInstance();
      console.log(`[Main Process] 🚀 开始调用SubprocessManager.startDetailScraper`);

      const result = await subprocessManager.startDetailScraper(trainerLink);
      console.log('[Main Process] ✅ 详情抓取完成:', JSON.stringify(result).substring(0, 200) + '...');

      // 发送响应到渲染进程
      if (mainWindow) {
        console.log(`[Main Process] 📤 发送响应到渲染进程`);
        mainWindow.webContents.send('trainer-download-options-response', result);
      }
    } catch (error: any) {
      console.error('[Main Process] ❌ 详情抓取失败:', error);
      const errorResult = {
        success: false,
        trainerLink,
        error: error.message || '获取下载选项失败'
      };

      // 发送错误响应到渲染进程
      if (mainWindow) {
        console.log(`[Main Process] 📤 发送错误响应到渲染进程`);
        mainWindow.webContents.send('trainer-download-options-response', errorResult);
      }
    }
  });

  // 调用新的加载函数
  loadMainWindow();
  
  // 在开发模式下自动打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    // mainWindow.webContents.openDevTools();
  }
  
  // 监听窗口关闭事件，清理资源
  mainWindow.on('closed', () => {
    console.log('[Main Process] 主窗口已关闭');
    mainWindow = null;
    
    // 子进程现在由SubprocessManager管理，会自动清理
  });
}

// 确保只有一个应用实例
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log('[Main Process] 已有实例正在运行，退出此实例');
  app.quit();
} else {
  app.on('second-instance', () => {
    // 有人试图启动第二个实例，我们应该聚焦到我们的窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });

  app.whenReady().then(() => {
    console.log('[Main Process] 应用已就绪，创建窗口');
    createWindow();

    // 当应用程序激活且没有窗口时创建窗口
    app.on('activate', function () {
      if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
  });
}

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 应用程序即将退出
app.on('before-quit', async () => {
  console.log('[Main Process] 应用程序即将退出，清理资源');

  // 关闭爬虫工作器
  if (scraperWorker) {
    try {
      console.log('[Main Process] 关闭爬虫工作器...');
      await scraperWorker.close();
      scraperWorker = null;
    } catch (err) {
      console.error('[Main Process] 关闭爬虫工作器时出错:', err);
    }
  }

  // 子进程现在由SubprocessManager管理，会自动清理
});

// 应用退出时清理资源
app.on('will-quit', () => {
  // 子进程现在由SubprocessManager管理，会自动清理
  console.log('[Main Process] 应用即将退出，清理资源');
});

