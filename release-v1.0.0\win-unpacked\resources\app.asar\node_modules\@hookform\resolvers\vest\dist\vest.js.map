{"version": 3, "file": "vest.js", "sources": ["../src/vest.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError } from 'react-hook-form';\nimport promisify from 'vest/promisify';\nimport type { Resolver, VestErrors } from './types';\n\nconst parseErrorSchema = (\n  vestError: VestErrors,\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (const path in vestError) {\n    if (!errors[path]) {\n      errors[path] = { message: vestError[path][0], type: '' };\n    }\n\n    if (validateAllFieldCriteria) {\n      errors[path].types = vestError[path].reduce<Record<number, string>>(\n        (acc, message, index) => (acc[index] = message) && acc,\n        {},\n      );\n    }\n  }\n  return errors;\n};\n\nexport const vestResolver: Resolver =\n  (schema, _, resolverOptions = {}) =>\n  async (values, context, options) => {\n    const result =\n      resolverOptions.mode === 'sync'\n        ? schema(values, options.names, context)\n        : await promisify(schema)(values, options.names, context);\n\n    if (result.hasErrors()) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            result.getErrors(),\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return { values, errors: {} };\n  };\n"], "names": ["parseErrorSchema", "vestError", "validateAllFieldCriteria", "errors", "path", "message", "type", "types", "reduce", "acc", "index", "schema", "_", "resolverOptions", "values", "context", "options", "_temp2", "result", "hasErrors", "toNestErrors", "getErrors", "shouldUseNativeValidation", "criteriaMode", "validateFieldsNatively", "Promise", "resolve", "mode", "names", "promisify", "then", "e", "reject"], "mappings": "6JAKMA,EAAmB,SACvBC,EACAC,GAEA,IAAMC,EAAqC,CAAE,EAC7C,IAAK,IAAMC,KAAQH,EACZE,EAAOC,KACVD,EAAOC,GAAQ,CAAEC,QAASJ,EAAUG,GAAM,GAAIE,KAAM,KAGlDJ,IACFC,EAAOC,GAAMG,MAAQN,EAAUG,GAAMI,OACnC,SAACC,EAAKJ,EAASK,GAAU,OAACD,EAAIC,GAASL,IAAYI,CAAG,EACtD,CAAE,IAIR,OAAON,CACT,uBAGE,SAACQ,EAAQC,EAAGC,GACLC,YADKD,IAAAA,IAAAA,EAAkB,CAAE,GACzBC,SAAAA,EAAQC,EAASC,GAAW,IAAA,IAAAC,EAAA,SAC3BC,GAKN,OAAIA,EAAOC,YACF,CACLL,OAAQ,CAAE,EACVX,OAAQiB,EAAYA,aAClBpB,EACEkB,EAAOG,aACNL,EAAQM,2BACkB,QAAzBN,EAAQO,cAEZP,KAKNA,EAAQM,2BAA6BE,EAAAA,uBAAuB,CAAA,EAAIR,GAEzD,CAAEF,OAAAA,EAAQX,OAAQ,CAAA,GAAK,EApBG,OAAAsB,QAAAC,QAAN,SAAzBb,EAAgBc,KAAeV,EAC3BN,EAAOG,EAAQE,EAAQY,MAAOb,IAAQU,QAAAC,QAChCG,EAAS,QAAClB,EAAVkB,CAAkBf,EAAQE,EAAQY,MAAOb,IAAQe,KAAAb,GAmB/D,CAAC,MAAAc,GAAAN,OAAAA,QAAAO,OAAAD,EAAA,CAAA,CAAA"}