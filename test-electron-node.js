const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 测试系统Node.js运行脚本（模拟SubprocessManager）');
console.log('=====================================');

// 使用系统Node.js运行脚本（模拟新的SubprocessManager逻辑）
const nodePath = 'node';
const scriptPath = path.join(__dirname, 'dist-release-final-fix', 'win-unpacked', 'resources', 'scraper', 'search-scraper.js');

console.log(`📁 Node.js路径: ${nodePath}`);
console.log(`📄 脚本路径: ${scriptPath}`);
console.log(`🔧 测试参数: "Cyberpunk 2077"`);
console.log('');

const testProcess = spawn(nodePath, [scriptPath, 'Cyberpunk 2077'], {
  env: {
    ...process.env,
    LANG: 'zh_CN.UTF-8',
    LC_ALL: 'zh_CN.UTF-8',
    LC_CTYPE: 'zh_CN.UTF-8',
    NODE_OPTIONS: '--no-warnings --max-old-space-size=1024'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let stdoutBuffer = '';
let stderrBuffer = '';

testProcess.stdout.on('data', (data) => {
  const output = data.toString();
  stdoutBuffer += output;
  console.log(`[STDOUT] ${output}`);
  
  // 查找RESULT:开头的JSON数据
  const resultMatch = stdoutBuffer.match(/RESULT:(.+)/);
  if (resultMatch) {
    try {
      const result = JSON.parse(resultMatch[1]);
      console.log('');
      console.log('🎉 成功获取结果:');
      console.log(JSON.stringify(result, null, 2));
      testProcess.kill();
    } catch (parseErr) {
      console.error('❌ 解析结果失败:', parseErr);
    }
  }
});

testProcess.stderr.on('data', (data) => {
  const output = data.toString();
  stderrBuffer += output;
  console.error(`[STDERR] ${output}`);
});

testProcess.on('error', (error) => {
  console.error('❌ 进程启动失败:', error);
});

testProcess.on('close', (code) => {
  console.log('');
  console.log(`📊 进程退出，代码: ${code}`);
  if (code !== 0) {
    console.log('❌ 进程异常退出');
    if (stderrBuffer) {
      console.log('错误输出:', stderrBuffer);
    }
  } else {
    console.log('✅ 进程正常退出');
  }
});

// 30秒超时
setTimeout(() => {
  console.log('⏰ 测试超时，终止进程');
  testProcess.kill();
}, 30000);
