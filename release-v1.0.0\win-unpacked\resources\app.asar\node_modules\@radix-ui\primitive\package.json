{"name": "@radix-ui/primitive", "version": "1.1.2", "license": "MIT", "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "publishConfig": {"main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}}, "files": ["dist", "README.md"], "sideEffects": false, "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "eslint": "^9.18.0", "typescript": "^5.7.3"}, "homepage": "https://radix-ui.com/primitives", "repository": {"type": "git", "url": "git+https://github.com/radix-ui/primitives.git"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "types": "./dist/index.d.ts"}