import puppeteer from 'puppeteer';
import { JSDOM } from 'jsdom';
import * as fs from 'fs';
import * as path from 'path';

// 定义数据接口
interface TrainerData {
  title: string;
  link: string;
  imageUrl: string;
  description: string;
  dateAdded: string;
  downloads: number;
  tags: string[];
}

interface DownloadOption {
  fileName: string;
  downloadUrl: string;
  dateAdded: string;
  fileSize: string;
  downloads: number;
}

export class ScraperService {
  private static instance: ScraperService;
  
  public static getInstance(): ScraperService {
    if (!ScraperService.instance) {
      ScraperService.instance = new ScraperService();
    }
    return ScraperService.instance;
  }

  // 获取Chromium可执行文件路径
  private getChromiumPath(): string | undefined {
    // 检查是否在打包环境中
    if (process.env.NODE_ENV === 'production' || (process as any).pkg || (process as any).resourcesPath) {
      console.log('[ScraperService] 检测到打包环境，查找内置Chromium');
      
      // 打包环境中的Chromium路径
      const possiblePaths = [
        path.join((process as any).resourcesPath, 'chromium', 'chrome.exe'),
        path.join((process as any).resourcesPath, '..', 'chromium', 'chrome.exe'),
        path.join(__dirname, '..', '..', 'chromium', 'chrome.exe'),
        path.join(process.cwd(), 'resources', 'chromium', 'chrome.exe')
      ];
      
      for (const chromiumPath of possiblePaths) {
        console.log(`[ScraperService] 检查Chromium路径: ${chromiumPath}`);
        if (fs.existsSync(chromiumPath)) {
          console.log(`[ScraperService] ✅ 找到Chromium: ${chromiumPath}`);
          return chromiumPath;
        }
      }
      
      console.error('[ScraperService] ❌ 未找到内置Chromium，将使用系统默认浏览器');
    }
    
    return undefined; // 使用Puppeteer默认的Chromium
  }

  // 翻译游戏名称
  public async translateGameName(gameName: string): Promise<string> {
    console.log(`[ScraperService] 🔤 开始翻译: "${gameName}"`);
    
    try {
      // 简单的翻译逻辑 - 可以扩展为使用在线翻译API
      const translations: { [key: string]: string } = {
        '黑神话悟空': 'Black Myth Wukong',
        '赛博朋克2077': 'Cyberpunk 2077',
        '巫师3': 'The Witcher 3',
        '上古卷轴5': 'The Elder Scrolls V Skyrim',
        '辐射4': 'Fallout 4',
        '侠盗猎车手5': 'Grand Theft Auto V',
        '使命召唤': 'Call of Duty',
        '战地': 'Battlefield',
        '刺客信条': 'Assassins Creed'
      };
      
      const translated = translations[gameName] || gameName;
      console.log(`[ScraperService] ✅ 翻译结果: "${gameName}" -> "${translated}"`);
      return translated;
    } catch (error) {
      console.error('[ScraperService] ❌ 翻译失败:', error);
      return gameName; // 翻译失败时返回原文
    }
  }

  // 搜索修改器
  public async searchTrainers(searchQuery: string): Promise<any> {
    console.log(`[ScraperService] 🔍 开始搜索: "${searchQuery}"`);
    
    // 获取Chromium路径
    const chromiumPath = this.getChromiumPath();
    
    let browser;
    try {
      // 优化浏览器启动选项
      const launchOptions: any = {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
          '--disable-extensions',
          '--disable-component-extensions-with-background-pages',
          '--disable-default-apps',
          '--mute-audio',
          '--no-default-browser-check',
          '--no-first-run',
          '--js-flags=--max-old-space-size=512'
        ],
        ignoreDefaultArgs: ['--enable-automation'],
        defaultViewport: { width: 1024, height: 768 }
      };
      
      // 如果找到了Chromium路径，则使用它
      if (chromiumPath) {
        launchOptions.executablePath = chromiumPath;
        console.log(`[ScraperService] 使用Chromium路径: ${chromiumPath}`);
      }
      
      browser = await puppeteer.launch(launchOptions);
      console.log('[ScraperService] 浏览器启动成功');
      
      const page = await browser.newPage();
      
      // 设置用户代理
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36');
      
      // 设置请求拦截
      await page.setRequestInterception(true);
      page.on('request', (request) => {
        const resourceType = request.resourceType();
        if (['image', 'font', 'stylesheet', 'media'].includes(resourceType)) {
          request.abort();
        } else {
          request.continue();
        }
      });
      
      // 设置超时
      page.setDefaultNavigationTimeout(30000);
      
      // 构建搜索URL
      const searchUrl = `https://flingtrainer.com/?s=${encodeURIComponent(searchQuery)}`;
      console.log(`[ScraperService] 访问搜索URL: ${searchUrl}`);
      
      await page.goto(searchUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
      
      // 等待页面加载完成
      await page.waitForSelector('body', { timeout: 10000 });
      
      // 获取页面HTML
      const html = await page.content();
      console.log(`[ScraperService] 获取到HTML，长度: ${html.length}`);
      
      // 关闭浏览器
      await browser.close();
      console.log('[ScraperService] 浏览器已关闭，开始解析搜索结果');
      
      // 解析搜索结果
      const searchResults = this.extractSearchResultsFromHTML(html);
      
      return {
        success: true,
        results: searchResults,
        query: searchQuery
      };
      
    } catch (error) {
      console.error('[ScraperService] 搜索过程中发生错误:', error);
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('[ScraperService] 关闭浏览器时出错:', closeError);
        }
      }
      
      return {
        success: false,
        results: [],
        error: error instanceof Error ? error.message : '搜索失败',
        query: searchQuery
      };
    }
  }

  // 从HTML中提取搜索结果
  private extractSearchResultsFromHTML(html: string): TrainerData[] {
    console.log('[ScraperService] 开始从HTML中提取搜索结果...');
    
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      
      const results: TrainerData[] = [];
      const articles = document.querySelectorAll('article.post');
      
      console.log(`[ScraperService] 找到 ${articles.length} 个文章元素`);
      
      articles.forEach((article, index) => {
        try {
          // 提取标题和链接 - 搜索页面使用不同的CSS类
          const titleElement = article.querySelector('h2.post-title a') ||
                              article.querySelector('h2.entry-title a') ||
                              article.querySelector('.post-title a') ||
                              article.querySelector('.entry-title a');
          const title = titleElement?.textContent?.trim() || '';
          const link = titleElement?.getAttribute('href') || '';
          
          // 提取图片
          const imgElement = article.querySelector('img');
          const imageUrl = imgElement?.getAttribute('src') || imgElement?.getAttribute('data-src') || '';
          
          // 提取描述 - 搜索页面可能有不同的结构
          const descElement = article.querySelector('.post-content .entry p') ||
                             article.querySelector('.entry-content p') ||
                             article.querySelector('.excerpt p') ||
                             article.querySelector('.entry p');
          const description = descElement?.textContent?.trim() || '';

          // 提取日期 - 搜索页面的日期结构
          const dateElement = article.querySelector('.post-details-day') ||
                             article.querySelector('.entry-date') ||
                             article.querySelector('time');
          let dateAdded = dateElement?.textContent?.trim() || '';

          // 如果找到了日期数字，尝试组合完整日期
          if (dateElement?.classList.contains('post-details-day')) {
            const monthElement = article.querySelector('.post-details-month');
            const yearElement = article.querySelector('.post-details-year');
            if (monthElement && yearElement) {
              dateAdded = `${dateElement.textContent?.trim()} ${monthElement.textContent?.trim()} ${yearElement.textContent?.trim()}`;
            }
          }
          
          // 提取下载次数（如果有）
          const downloadsElement = article.querySelector('.download-count');
          const downloads = parseInt(downloadsElement?.textContent?.replace(/\D/g, '') || '0');
          
          // 提取标签
          const tagElements = article.querySelectorAll('.entry-tags a, .post-tags a');
          const tags: string[] = [];
          tagElements.forEach(tag => {
            const tagText = tag.textContent?.trim();
            if (tagText) tags.push(tagText);
          });
          
          if (title && link) {
            results.push({
              title,
              link,
              imageUrl,
              description,
              dateAdded,
              downloads,
              tags
            });
            
            console.log(`[ScraperService] 提取结果 ${index + 1}: ${title}`);
          }
        } catch (error) {
          console.error(`[ScraperService] 提取第 ${index + 1} 个结果时出错:`, error);
        }
      });
      
      console.log(`[ScraperService] 成功提取 ${results.length} 条搜索结果`);
      return results;
      
    } catch (error) {
      console.error('[ScraperService] 解析HTML时出错:', error);
      return [];
    }
  }

  // 获取下载选项
  public async getDownloadOptions(trainerLink: string): Promise<any> {
    console.log(`[ScraperService] 📥 开始获取下载选项: ${trainerLink}`);

    // 获取Chromium路径
    const chromiumPath = this.getChromiumPath();

    let browser;
    try {
      // 优化浏览器启动选项，减少内存使用
      const launchOptions: any = {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
          '--js-flags=--max-old-space-size=512'
        ],
        ignoreDefaultArgs: ['--enable-automation'],
        defaultViewport: { width: 1280, height: 1024 }
      };

      // 如果找到了Chromium路径，则使用它
      if (chromiumPath) {
        launchOptions.executablePath = chromiumPath;
        console.log(`[ScraperService] 使用Chromium路径: ${chromiumPath}`);
      }

      browser = await puppeteer.launch(launchOptions);
      const page = await browser.newPage();

      // 设置请求拦截，阻止不必要的资源加载
      await page.setRequestInterception(true);
      page.on('request', (req) => {
        const resourceType = req.resourceType();
        if (
          resourceType === 'image' ||
          resourceType === 'font' ||
          resourceType === 'stylesheet' ||
          resourceType === 'media'
        ) {
          req.abort();
        } else {
          req.continue();
        }
      });

      // 设置更短的超时时间
      page.setDefaultNavigationTimeout(30000);

      console.log(`[ScraperService] 正在访问页面: ${trainerLink}`);
      await page.goto(trainerLink, { waitUntil: 'domcontentloaded', timeout: 30000 });

      // 等待页面加载完成
      await page.waitForSelector('body', { timeout: 10000 });

      console.log('[ScraperService] 页面已加载，开始查找下载链接');

      // 获取页面内容
      const html = await page.content();

      // 关闭浏览器
      await browser.close();
      console.log('[ScraperService] 浏览器已关闭');

      // 解析下载选项
      const downloadOptions = this.extractDownloadOptionsFromHTML(html);

      if (downloadOptions.length === 0) {
        console.warn('[ScraperService] 未找到任何下载选项');
        return {
          success: false,
          trainerLink,
          error: '未找到任何下载选项'
        };
      } else {
        console.log(`[ScraperService] 成功找到 ${downloadOptions.length} 个下载选项`);
        return {
          success: true,
          trainerLink,
          downloadOptions
        };
      }

    } catch (error) {
      console.error('[ScraperService] 获取下载选项时发生错误:', error);
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('[ScraperService] 关闭浏览器时出错:', closeError);
        }
      }

      return {
        success: false,
        trainerLink,
        error: error instanceof Error ? error.message : '获取下载选项失败'
      };
    }
  }

  // 从HTML中提取下载选项
  private extractDownloadOptionsFromHTML(html: string): DownloadOption[] {
    console.log('[ScraperService] 开始从HTML中提取下载选项...');

    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;

      const downloadOptions: DownloadOption[] = [];

      // 查找下载链接的多种选择器
      const selectors = [
        'a[href*="download"]',
        'a[href*=".zip"]',
        'a[href*=".rar"]',
        'a[href*=".7z"]',
        '.download-link a',
        '.download-button a',
        '.btn-download',
        'a.download'
      ];

      for (const selector of selectors) {
        const links = document.querySelectorAll(selector);

        links.forEach((link, index) => {
          const href = link.getAttribute('href');
          const text = link.textContent?.trim() || '';

          if (href && (href.includes('download') || href.match(/\.(zip|rar|7z|exe)$/i))) {
            // 尝试提取文件信息
            const fileName = this.extractFileName(text, href);
            const downloadUrl = href.startsWith('http') ? href : `https://flingtrainer.com${href}`;

            // 查找相关的文件信息
            const parent = link.closest('tr, .download-item, .file-item') || link.parentElement;
            const dateAdded = this.extractDateFromElement(parent) || '';
            const fileSize = this.extractFileSizeFromElement(parent) || '';
            const downloads = this.extractDownloadCountFromElement(parent) || 0;

            downloadOptions.push({
              fileName,
              downloadUrl,
              dateAdded,
              fileSize,
              downloads
            });

            console.log(`[ScraperService] 找到下载选项: ${fileName}`);
          }
        });
      }

      // 去重
      const uniqueOptions = downloadOptions.filter((option, index, self) =>
        index === self.findIndex(o => o.downloadUrl === option.downloadUrl)
      );

      console.log(`[ScraperService] 提取到 ${uniqueOptions.length} 个唯一下载选项`);
      return uniqueOptions;

    } catch (error) {
      console.error('[ScraperService] 解析下载选项HTML时出错:', error);
      return [];
    }
  }

  // 辅助方法：提取文件名
  private extractFileName(text: string, href: string): string {
    // 从链接文本中提取文件名
    if (text && text.length > 0 && !text.toLowerCase().includes('download')) {
      return text;
    }

    // 从URL中提取文件名
    const urlParts = href.split('/');
    const lastPart = urlParts[urlParts.length - 1];
    if (lastPart && lastPart.includes('.')) {
      return decodeURIComponent(lastPart);
    }

    return 'Download File';
  }

  // 辅助方法：提取日期
  private extractDateFromElement(element: Element | null): string {
    if (!element) return '';

    const dateSelectors = ['.date', '.upload-date', '.added-date', 'time'];
    for (const selector of dateSelectors) {
      const dateElement = element.querySelector(selector);
      if (dateElement) {
        return dateElement.textContent?.trim() || '';
      }
    }

    return '';
  }

  // 辅助方法：提取文件大小
  private extractFileSizeFromElement(element: Element | null): string {
    if (!element) return '';

    const sizeSelectors = ['.size', '.file-size', '.filesize'];
    for (const selector of sizeSelectors) {
      const sizeElement = element.querySelector(selector);
      if (sizeElement) {
        return sizeElement.textContent?.trim() || '';
      }
    }

    // 在文本中查找文件大小模式
    const text = element.textContent || '';
    const sizeMatch = text.match(/(\d+(?:\.\d+)?\s*(?:KB|MB|GB))/i);
    if (sizeMatch) {
      return sizeMatch[1];
    }

    return '';
  }

  // 辅助方法：提取下载次数
  private extractDownloadCountFromElement(element: Element | null): number {
    if (!element) return 0;

    const countSelectors = ['.downloads', '.download-count', '.count'];
    for (const selector of countSelectors) {
      const countElement = element.querySelector(selector);
      if (countElement) {
        const countText = countElement.textContent?.trim() || '';
        const count = parseInt(countText.replace(/\D/g, ''));
        if (!isNaN(count)) {
          return count;
        }
      }
    }

    return 0;
  }
}
