{"version": 3, "file": "ajv.js", "sources": ["../src/ajv.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport Ajv, { DefinedError } from 'ajv';\nimport ajvErrors from 'ajv-errors';\nimport { FieldError, appendErrors } from 'react-hook-form';\nimport { Resolver } from './types';\n\nconst parseErrorSchema = (\n  ajvErrors: DefinedError[],\n  validateAllFieldCriteria: boolean,\n) => {\n  // Ajv will return empty instancePath when require error\n  ajvErrors.forEach((error) => {\n    if (error.keyword === 'required') {\n      error.instancePath += '/' + error.params.missingProperty;\n    }\n  });\n\n  return ajvErrors.reduce<Record<string, FieldError>>((previous, error) => {\n    // `/deepObject/data` -> `deepObject.data`\n    const path = error.instancePath.substring(1).replace(/\\//g, '.');\n\n    if (!previous[path]) {\n      previous[path] = {\n        message: error.message,\n        type: error.keyword,\n      };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = previous[path].types;\n      const messages = types && types[error.keyword];\n\n      previous[path] = appendErrors(\n        path,\n        validateAllFieldCriteria,\n        previous,\n        error.keyword,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message || '')\n          : error.message,\n      ) as FieldError;\n    }\n\n    return previous;\n  }, {});\n};\n\nexport const ajvResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    const ajv = new Ajv(\n      Object.assign(\n        {},\n        {\n          allErrors: true,\n          validateSchema: true,\n        },\n        schemaOptions,\n      ),\n    );\n\n    ajvErrors(ajv);\n\n    const validate = ajv.compile(\n      Object.assign(\n        { $async: resolverOptions && resolverOptions.mode === 'async' },\n        schema,\n      ),\n    );\n\n    const valid = validate(values);\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return valid\n      ? { values, errors: {} }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validate.errors as DefinedError[],\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n"], "names": ["parseErrorSchema", "ajvErrors", "validateAllFieldCriteria", "for<PERSON>ach", "error", "keyword", "instancePath", "params", "missingProperty", "reduce", "previous", "path", "substring", "replace", "message", "type", "types", "messages", "appendErrors", "concat", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "ajv", "Ajv", "Object", "assign", "allErrors", "validateSchema", "validate", "compile", "$async", "mode", "valid", "shouldUseNativeValidation", "validateFieldsNatively", "Promise", "resolve", "errors", "toNestErrors", "criteriaMode", "e", "reject"], "mappings": "+NAMMA,EAAmB,SACvBC,EACAC,GASA,OANAD,EAAUE,QAAQ,SAACC,GACK,aAAlBA,EAAMC,UACRD,EAAME,cAAgB,IAAMF,EAAMG,OAAOC,gBAE7C,GAEOP,EAAUQ,OAAmC,SAACC,EAAUN,GAE7D,IAAMO,EAAOP,EAAME,aAAaM,UAAU,GAAGC,QAAQ,MAAO,KAS5D,GAPKH,EAASC,KACZD,EAASC,GAAQ,CACfG,QAASV,EAAMU,QACfC,KAAMX,EAAMC,UAIZH,EAA0B,CAC5B,IAAMc,EAAQN,EAASC,GAAMK,MACvBC,EAAWD,GAASA,EAAMZ,EAAMC,SAEtCK,EAASC,GAAQO,eACfP,EACAT,EACAQ,EACAN,EAAMC,QACNY,EACK,GAAgBE,OAAOF,EAAsBb,EAAMU,SAAW,IAC/DV,EAAMU,QAEd,CAEA,OAAOJ,CACT,EAAG,GACL,sBAGE,SAACU,EAAQC,EAAeC,GAAoB,gBAApBA,IAAAA,EAAkB,CAAA,GAAE,SACrCC,EAAQC,EAAGC,GAAO,IACvB,IAAMC,EAAM,IAAIC,UACdC,OAAOC,OACL,CAAE,EACF,CACEC,WAAW,EACXC,gBAAgB,GAElBV,IAIJpB,EAAS,QAACyB,GAEV,IAAMM,EAAWN,EAAIO,QACnBL,OAAOC,OACL,CAAEK,OAAQZ,GAA4C,UAAzBA,EAAgBa,MAC7Cf,IAIEgB,EAAQJ,EAAST,GAIvB,OAFAE,EAAQY,2BAA6BC,EAAAA,uBAAuB,CAAE,EAAEb,GAEhEc,QAAAC,QAAOJ,EACH,CAAEb,OAAAA,EAAQkB,OAAQ,CAAI,GACtB,CACElB,OAAQ,CAAA,EACRkB,OAAQC,eACN1C,EACEgC,EAASS,QACRhB,EAAQY,2BACkB,QAAzBZ,EAAQkB,cAEZlB,IAGV,CAAC,MAAAmB,GAAA,OAAAL,QAAAM,OAAAD,EAAA,CAAA,CAAA"}