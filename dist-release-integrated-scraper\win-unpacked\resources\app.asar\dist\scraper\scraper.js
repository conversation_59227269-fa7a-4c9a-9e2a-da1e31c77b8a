"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const puppeteer_extra_1 = __importDefault(require("puppeteer-extra"));
const puppeteer_extra_plugin_stealth_1 = __importDefault(require("puppeteer-extra-plugin-stealth"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const jsdom_1 = require("jsdom");
// 设置正确的编码，解决中文显示问题
process.env.LANG = 'zh_CN.UTF-8';
process.env.LC_ALL = 'zh_CN.UTF-8';
process.env.LC_CTYPE = 'zh_CN.UTF-8';
// 在Windows平台下，设置控制台代码页为UTF-8
if (process.platform === 'win32') {
    try {
        const { execSync } = require('child_process');
        execSync('chcp 65001', { stdio: 'ignore' });
    }
    catch (err) {
        console.error('设置控制台代码页失败:', err);
    }
}
// 确保可以正确处理中文字符
function ensureUtf8(str) {
    try {
        // 如果已经是有效的UTF-8字符串，直接返回
        if (Buffer.from(str).toString('utf8') === str) {
            return str;
        }
        // 尝试解码可能的非UTF-8编码
        const buffer = Buffer.from(str, 'binary');
        return buffer.toString('utf8');
    }
    catch (e) {
        console.error('字符串编码转换失败:', e);
        return str; // 失败时返回原始字符串
    }
}
console.log('--- 开始执行 V8 版高效抓取脚本 ---');
// 使用隐身插件
puppeteer_extra_1.default.use((0, puppeteer_extra_plugin_stealth_1.default)());
// 直接从HTML字符串中提取数据的函数，减少内存使用
function extractDataFromHTML(html) {
    console.log('[Scraper Script] 开始从HTML字符串中提取数据...');
    try {
        // 创建一个临时的DOM解析环境，减少内存占用
        const dom = new jsdom_1.JSDOM(html, {
            pretendToBeVisual: false, // 禁用视觉模拟
            runScripts: "outside-only" // 不运行脚本
        });
        const document = dom.window.document;
        // 限制处理的文章数量，减少内存使用
        const articles = Array.from(document.querySelectorAll('article')).slice(0, 15);
        console.log(`[Scraper Script] 找到 ${articles.length} 个文章元素`);
        if (articles.length === 0) {
            // 仅保存HTML的前5000个字符，避免大文件
            fs_1.default.writeFileSync(path_1.default.join(__dirname, 'debug_html.txt'), html.substring(0, 5000));
            console.log('[Scraper Script] 未找到文章元素，已保存部分HTML供调试');
        }
        const data = [];
        // 逐个处理文章，而不是使用map/filter等高阶函数，减少内存压力
        for (const article of articles) {
            try {
                // 1. 提取标题和链接
                const titleElement = article.querySelector('h2.post-title a');
                const title = titleElement?.textContent?.trim() || '';
                const link = titleElement?.getAttribute('href') || '';
                // 如果没有标题或链接，跳过此项
                if (!title || !link)
                    continue;
                // 2. 提取选项数量、游戏版本和更新日期
                const entryElement = article.querySelector('div.entry');
                const metaText = entryElement?.textContent?.trim() || '';
                // 提取选项数量
                const optionsMatch = metaText.match(/(\d+)\s+Options/i);
                const options = optionsMatch ? parseInt(optionsMatch[1], 10) : 0;
                // 提取游戏版本
                const gameVersionMatch = metaText.match(/Game Version:\s*([^·]+?)(?:\s*·|$)/);
                const gameVersion = gameVersionMatch ? gameVersionMatch[1].trim() : 'N/A';
                // 提取更新日期
                const lastUpdatedMatch = metaText.match(/Last Updated:\s*([\d.]+)/);
                const lastUpdated = lastUpdatedMatch ? lastUpdatedMatch[1].trim() : 'N/A';
                // 3. 提取图片URL
                const imageElement = article.querySelector('img.attachment-stylizer-small');
                let imageUrl = imageElement?.getAttribute('src') || '';
                // 处理没有特色图片的情况
                if (!imageUrl) {
                    imageUrl = 'https://flingtrainer.com/wp-content/themes/stylizer/images/default-thumb.png';
                }
                // 添加到数据数组
                data.push({
                    title,
                    link,
                    options,
                    gameVersion,
                    lastUpdated,
                    imageUrl
                });
            }
            catch (error) {
                console.error('[Scraper Script] 解析单个文章时出错:', error);
            }
        }
        console.log(`[Scraper Script] 从HTML中成功提取 ${data.length} 条数据`);
        // 释放DOM内存
        dom.window.close();
        return data;
    }
    catch (error) {
        console.error('[Scraper Script] 从HTML提取数据时出错:', error);
        return [];
    }
}
// 使用Puppeteer抓取网站数据
async function scrapeFlingTrainer(page = 1, chromiumPath) {
    console.log(`[Scraper Script] 开始执行抓取任务，页码: ${page}...`);
    if (chromiumPath) {
        console.log(`[Scraper Script] 使用指定的Chromium路径: ${chromiumPath}`);
    }
    let browser = null;
    try {
        // 优化浏览器启动选项，减少内存使用
        console.log('[Scraper Script] 准备启动Puppeteer浏览器...');
        browser = await puppeteer_extra_1.default.launch({
            executablePath: chromiumPath,
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage', // 减少/dev/shm使用，避免内存问题
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--js-flags=--max-old-space-size=1024' // 增加JS堆大小
            ],
            ignoreDefaultArgs: ['--enable-automation'],
            defaultViewport: { width: 1280, height: 800 }, // 模拟更真实的浏览器窗口
            timeout: 60000 // 增加浏览器启动超时到60秒
        });
        console.log('[Scraper Script] Puppeteer浏览器启动成功');
        const page_instance = await browser.newPage();
        console.log('[Scraper Script] 新页面创建成功');
        // 设置请求拦截，阻止不必要的资源加载
        await page_instance.setRequestInterception(true);
        page_instance.on('request', (request) => {
            const resourceType = request.resourceType();
            if (['image', 'font', 'stylesheet', 'media'].includes(resourceType)) {
                request.abort();
            }
            else {
                request.continue();
            }
        });
        // 设置更长的导航超时
        page_instance.setDefaultNavigationTimeout(60000); // 导航超时增加到60秒
        const url = page === 1 ? 'https://flingtrainer.com' : `https://flingtrainer.com/page/${page}/`;
        console.log(`[Scraper Script] 正在访问网站: ${url}`);
        await page_instance.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: 60000 // goto超时也增加到60秒
        });
        console.log('[Scraper Script] 页面加载完成，获取HTML内容...');
        const html = await page_instance.content();
        console.log('[Scraper Script] HTML内容获取成功');
        // 检查分页信息
        const hasPrevPage = await page_instance.evaluate(() => {
            const links = Array.from(document.querySelectorAll('a'));
            return links.some(link => link.textContent && link.textContent.trim() === '« Previous Page');
        });
        const hasNextPage = await page_instance.evaluate(() => {
            const links = Array.from(document.querySelectorAll('a'));
            return links.some(link => link.textContent && link.textContent.trim() === 'Next Page »');
        });
        console.log(`[Scraper Script] 分页检查结果 - 上一页: ${hasPrevPage}, 下一页: ${hasNextPage}`);
        await page_instance.close();
        console.log('[Scraper Script] 页面已关闭');
        await browser.close();
        console.log('[Scraper Script] 浏览器已关闭，开始解析数据');
        const data = extractDataFromHTML(html);
        const pagination = {
            currentPage: page,
            hasNextPage,
            hasPrevPage,
            totalPages: 10 // 假设有10页，实际情况可能需要动态计算
        };
        return { data, pagination };
    }
    catch (error) {
        const err = error;
        console.error('[Scraper Script] 抓取过程中发生严重错误:', err.stack);
        if (browser) {
            try {
                await browser.close();
                console.log('[Scraper Script] 错误发生后，浏览器已关闭');
            }
            catch (closeError) {
                console.error('[Scraper Script] 尝试关闭浏览器时出错:', closeError);
            }
        }
        // 抛出错误以便主函数能捕获并发送IPC消息
        throw err;
    }
}
// 主函数
async function main() {
    try {
        // 获取命令行参数中的页码和浏览器路径
        const pageArg = process.argv[2];
        const chromiumPathArg = process.argv[3];
        const page = pageArg ? parseInt(pageArg, 10) : 1;
        console.log(`[Scraper Script] 开始抓取第 ${page} 页数据...`);
        try {
            const { data, pagination } = await scrapeFlingTrainer(page, chromiumPathArg);
            if (process.send) {
                console.log('[Scraper Script] 通过IPC发送成功数据到主进程...');
                process.send({ success: true, data, pagination });
                console.log('[Scraper Script] 数据已成功发送');
            }
            else {
                console.error('[Scraper Script] 无法发送数据，因为进程没有IPC通道');
            }
        }
        catch (error) {
            const err = error;
            console.error('[Scraper Script] 执行过程中发生错误:', err.message);
            if (process.send) {
                process.send({ success: false, error: `执行过程中发生错误: ${err.message}` });
            }
        }
    }
    catch (error) {
        console.error('[Scraper Script] 执行过程中发生错误:', error);
        process.send({ success: false, error: `执行过程中发生错误: ${error}` });
    }
}
// 添加全局错误处理
process.on('uncaughtException', (error) => {
    console.error('[Scraper Script] 未捕获的异常:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason) => {
    console.error('[Scraper Script] 未处理的Promise拒绝:', reason);
});
// 设置超时，避免进程挂起
setTimeout(() => {
    console.error('[Scraper Script] 脚本执行超时，强制退出');
    process.exit(1);
}, 90000); // 增加到90秒超时
// 执行主函数
main();
