const { spawn } = require('child_process');
const path = require('path');

console.log('🎯 测试IPC通信修复版本');
console.log('=====================================');

// 测试便携版
const portableExe = path.join(__dirname, 'dist-release-final-fix', '游戏修改器盒子-便携版.exe');

console.log(`📦 启动修复版应用: ${portableExe}`);

const testProcess = spawn(portableExe, [], {
  stdio: 'inherit',
  detached: true
});

testProcess.on('spawn', () => {
  console.log('✅ 应用启动成功！');
  console.log('');
  console.log('🔧 **关键修复内容**：');
  console.log('   ✅ 添加了缺失的IPC处理器：request-trainer-download-options');
  console.log('   ✅ 修复了渲染进程与主进程的通信不匹配问题');
  console.log('   ✅ 使用系统Node.js运行脚本，避免Electron兼容性问题');
  console.log('   ✅ 脚本文件位于正确路径：resources/scraper/');
  console.log('');
  console.log('🧪 **请按以下步骤测试**：');
  console.log('');
  console.log('📥 **1. 下载功能测试（重点）**：');
  console.log('   - 在主页面找到任意修改器');
  console.log('   - 点击"立即下载"按钮');
  console.log('   - 🎯 **预期**：下载选项对话框应该正常显示内容');
  console.log('   - ❌ **之前**：显示"获取下载选项超时"错误');
  console.log('');
  console.log('🔍 **2. 搜索功能测试**：');
  console.log('   - 输入"Cyberpunk 2077"或"黑神话悟空"');
  console.log('   - 点击搜索按钮');
  console.log('   - 🎯 **预期**：能正常返回搜索结果');
  console.log('   - ❌ **之前**：卡在"正在翻译..."状态');
  console.log('');
  console.log('🔧 **3. 调试信息检查**：');
  console.log('   - 按F12打开开发者工具');
  console.log('   - 查看Console标签页');
  console.log('   - 🎯 **应该看到**：');
  console.log('     * [Main Process] 🎯 收到获取下载选项请求');
  console.log('     * [SubprocessManager] 📥 开始获取下载选项');
  console.log('     * [SubprocessManager] ✅ 详情抓取进程启动成功');
  console.log('     * [Main Process] ✅ 详情抓取完成');
  console.log('     * [Main Process] 📤 发送响应到渲染进程');
  console.log('');
  console.log('💡 **修复原理**：');
  console.log('   - 渲染进程发送：request-trainer-download-options');
  console.log('   - 主进程监听：request-trainer-download-options ✅ (之前缺失)');
  console.log('   - 响应事件：trainer-download-options-response');
  console.log('');
  console.log('⚠️  **如果问题仍然存在**，请报告：');
  console.log('   1. Console中是否看到🎯标记的日志');
  console.log('   2. 是否看到[SubprocessManager]相关日志');
  console.log('   3. 具体的错误信息');
  console.log('');
  console.log('🎉 **基于脚本独立测试成功 + IPC修复，这次应该能完全解决问题！**');
  console.log('');
  console.log('⚠️  测试完成后请手动关闭应用');
});

testProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

// 10秒后分离进程，让用户自己测试
setTimeout(() => {
  testProcess.unref();
  console.log('🔄 进程已分离，可以独立运行');
}, 10000);
