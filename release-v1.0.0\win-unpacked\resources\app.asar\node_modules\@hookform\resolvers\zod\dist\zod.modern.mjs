import{validateFieldsNatively as r,toNestErrors as o}from"@hookform/resolvers";import{appendErrors as s}from"react-hook-form";const e=(r,o)=>{const e={};for(;r.length;){const a=r[0],{code:t,message:n,path:i}=a,c=i.join(".");if(!e[c])if("unionErrors"in a){const r=a.unionErrors[0].errors[0];e[c]={message:r.message,type:r.code}}else e[c]={message:n,type:t};if("unionErrors"in a&&a.unionErrors.forEach(o=>o.errors.forEach(o=>r.push(o))),o){const r=e[c].types,n=r&&r[a.code];e[c]=s(c,o,e,t,n?[].concat(n,a.message):a.message)}r.shift()}return e},a=(s,a,t={})=>async(n,i,c)=>{try{const o=await s["sync"===t.mode?"parse":"parseAsync"](n,a);return c.shouldUseNativeValidation&&r({},c),{errors:{},values:t.raw?n:o}}catch(r){if((r=>Array.isArray(null==r?void 0:r.errors))(r))return{values:{},errors:o(e(r.errors,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw r}};export{a as zodResolver};
//# sourceMappingURL=zod.modern.mjs.map
