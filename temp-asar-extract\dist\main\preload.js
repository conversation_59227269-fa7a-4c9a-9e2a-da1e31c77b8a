(()=>{"use strict";var e={157:e=>{e.exports=require("electron")}},r={};function o(n){var d=r[n];if(void 0!==d)return d.exports;var t=r[n]={exports:{}};return e[n](t,t.exports,o),t.exports}(()=>{const e=o(157);e.contextBridge.exposeInMainWorld("electron",{windowMinimize:()=>e.ipcRenderer.send("window-minimize"),windowMaximize:()=>e.ipcRenderer.send("window-maximize"),windowClose:()=>e.ipcRenderer.send("window-close"),shell:{openExternal:r=>{["https://flingtrainer.com/","https://flingtrainer.com","https://www.baidu.com/"].some(e=>r===e||r.startsWith(`${e}/`))&&e.shell.openExternal(r)}},ipcRenderer:{send:(r,...o)=>{["renderer-ready","request-trainer-data","request-trainer-download-options","download-file","get-downloads","clear-completed-downloads","delete-download","set-download-folder","get-download-folder","search-trainers","open-external-url"].includes(r)&&e.ipcRenderer.send(r,...o)},invoke:async(r,...o)=>["get-download-options","get-download-folder","get-app-info","get-system-info","translate-game-name"].includes(r)?await e.ipcRenderer.invoke(r,...o):null,on:(r,o)=>{if(["update-trainer-data","update-trainer-data-error","trainer-download-options-response","download-update","downloads-list-update","download-folder","download-folder-updated","search-results-response","download-completed"].includes(r)){const n=(e,...r)=>o(...r);return e.ipcRenderer.on(r,n),()=>{e.ipcRenderer.removeListener(r,n)}}return()=>{}},removeListener:(r,o)=>{["update-trainer-data","update-trainer-data-error","trainer-download-options-response","download-update","downloads-list-update","download-folder","download-folder-updated","search-results-response"].includes(r)&&e.ipcRenderer.removeListener(r,o)}}})})()})();