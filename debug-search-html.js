const puppeteer = require('puppeteer');
const fs = require('fs');

async function debugSearchHTML() {
  console.log('🔍 开始调试搜索页面HTML结构...');
  
  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口以便观察
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 访问搜索页面
    const searchQuery = 'Cyberpunk 2077';
    const searchUrl = `https://flingtrainer.com/?s=${encodeURIComponent(searchQuery)}`;
    console.log(`访问搜索URL: ${searchUrl}`);
    
    await page.goto(searchUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
    await page.waitForSelector('body', { timeout: 10000 });
    
    // 获取页面HTML
    const html = await page.content();
    console.log(`获取到HTML，长度: ${html.length}`);
    
    // 保存HTML到文件
    fs.writeFileSync('search-page-debug.html', html, 'utf8');
    console.log('✅ HTML已保存到 search-page-debug.html');
    
    // 分析页面结构
    console.log('\n🔍 分析页面结构...');
    
    // 检查各种可能的选择器
    const selectors = [
      'article.post',
      'article',
      '.post',
      '.search-result',
      '.entry',
      '.result-item',
      'div[class*="post"]',
      'div[class*="entry"]',
      'div[class*="result"]',
      '.hentry',
      '.post-item',
      '.content-item'
    ];
    
    for (const selector of selectors) {
      const elements = await page.$$(selector);
      console.log(`${selector}: 找到 ${elements.length} 个元素`);
    }
    
    // 检查页面是否有"没有找到结果"的提示
    const noResultsSelectors = [
      '.no-results',
      '.not-found',
      '.search-no-results',
      'div:contains("没有找到")',
      'div:contains("No results")',
      'div:contains("未找到")'
    ];
    
    console.log('\n🔍 检查"无结果"提示...');
    for (const selector of noResultsSelectors) {
      try {
        const elements = await page.$$(selector);
        if (elements.length > 0) {
          const text = await page.evaluate(el => el.textContent, elements[0]);
          console.log(`${selector}: 找到 ${elements.length} 个元素，内容: "${text}"`);
        }
      } catch (e) {
        // 忽略CSS选择器错误
      }
    }
    
    // 获取页面标题
    const title = await page.title();
    console.log(`\n页面标题: ${title}`);
    
    // 检查是否有搜索结果容器
    const mainContent = await page.$('main, #main, .main, .content, #content');
    if (mainContent) {
      const mainHTML = await page.evaluate(el => el.innerHTML, mainContent);
      console.log(`主内容区域HTML长度: ${mainHTML.length}`);
      
      // 保存主内容区域
      fs.writeFileSync('search-main-content.html', mainHTML, 'utf8');
      console.log('✅ 主内容区域HTML已保存到 search-main-content.html');
    }
    
    await browser.close();
    console.log('\n✅ 调试完成！请检查生成的HTML文件来分析页面结构。');
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
    if (browser) {
      await browser.close();
    }
  }
}

debugSearchHTML();
