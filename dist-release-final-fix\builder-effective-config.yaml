directories:
  output: dist-release-final-fix
  buildResources: assets
appId: com.flingtrainer.gamemodifierbox
productName: 游戏修改器盒子
copyright: Copyright © 2024 游戏修改器盒子
files:
  - filter:
      - dist/**/*
      - package.json
      - '!src/**/*'
      - '!*.md'
      - '!*.config.*'
extraResources:
  - from: dist/scraper
    to: scraper
    filter:
      - '**/*'
  - from: chrome-browser
    to: chromium
    filter:
      - '**/*'
win:
  icon: assets/icon.ico
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: 游戏修改器盒子
  installerIcon: assets/icon.ico
  uninstallerIcon: assets/icon.ico
  installerHeaderIcon: assets/icon.ico
  deleteAppDataOnUninstall: false
  runAfterFinish: true
  menuCategory: 游戏工具
portable:
  artifactName: 游戏修改器盒子-便携版.exe
extraMetadata:
  main: dist/main/main.js
  homepage: https://flingtrainer.com
  description: 一站式游戏修改器下载和管理工具
  author:
    name: 游戏修改器盒子团队
    email: <EMAIL>
publish: null
compression: maximum
includeSubNodeModules: true
npmRebuild: true
asar: true
asarUnpack:
  - dist/scraper/**/*
  - node_modules/**/*
electronVersion: 31.0.1
