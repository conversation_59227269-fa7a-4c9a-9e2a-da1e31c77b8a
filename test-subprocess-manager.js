const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 测试SubprocessManager修复效果');
console.log('=====================================');

// 测试便携版
const portableExe = path.join(__dirname, 'dist-release-final-fix', '游戏修改器盒子-便携版.exe');

console.log(`📦 启动便携版: ${portableExe}`);

const testProcess = spawn(portableExe, [], {
  stdio: 'inherit',
  detached: true
});

testProcess.on('spawn', () => {
  console.log('✅ 应用启动成功！');
  console.log('');
  console.log('🔍 请按以下步骤测试：');
  console.log('');
  console.log('1. 📝 **搜索功能测试**：');
  console.log('   - 在搜索框输入"Cyberpunk 2077"');
  console.log('   - 点击搜索按钮');
  console.log('   - 观察是否卡在"正在翻译..."状态');
  console.log('   - 检查是否能正常返回搜索结果');
  console.log('');
  console.log('2. 📥 **下载功能测试**：');
  console.log('   - 在主页面找到任意修改器');
  console.log('   - 点击"立即下载"按钮');
  console.log('   - 观察下载选项对话框是否正常显示内容');
  console.log('   - 检查是否显示"获取下载选项超时"错误');
  console.log('');
  console.log('3. 🔧 **调试信息检查**：');
  console.log('   - 按F12打开开发者工具');
  console.log('   - 查看Console标签页');
  console.log('   - 寻找[SubprocessManager]开头的调试信息');
  console.log('   - 确认脚本路径是否正确找到');
  console.log('');
  console.log('💡 **预期结果**：');
  console.log('   ✅ 搜索功能：能正常翻译并返回结果');
  console.log('   ✅ 下载功能：下载选项对话框正常显示');
  console.log('   ✅ 调试信息：显示详细的SubprocessManager日志');
  console.log('');
  console.log('⚠️  测试完成后请手动关闭应用');
});

testProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

// 10秒后分离进程，让用户自己测试
setTimeout(() => {
  testProcess.unref();
  console.log('🔄 进程已分离，可以独立运行');
  console.log('');
  console.log('📋 **如果问题仍然存在，请报告以下信息**：');
  console.log('   1. 搜索时是否卡在"正在翻译..."');
  console.log('   2. 下载时是否显示"获取下载选项超时"');
  console.log('   3. 开发者工具中的错误信息');
  console.log('   4. [SubprocessManager]相关的日志内容');
}, 10000);
