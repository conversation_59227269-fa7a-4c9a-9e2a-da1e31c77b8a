{"version": 3, "sources": ["../src/focus-scope.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAwM3B;AAtMJ,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAK;AAQzD,IAAM,mBAAmB;AAgCzB,IAAM,aAAmB,iBAA+C,CAAC,OAAO,iBAAiB;AAC/F,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,WAAW,YAAY,IAAU,eAA6B,IAAI;AACzE,QAAM,mBAAmB,eAAe,oBAAoB;AAC5D,QAAM,qBAAqB,eAAe,sBAAsB;AAChE,QAAM,wBAA8B,aAA2B,IAAI;AACnE,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,aAAa,IAAI,CAAC;AAE/E,QAAM,aAAmB,aAAO;AAAA,IAC9B,QAAQ;AAAA,IACR,QAAQ;AACN,WAAK,SAAS;AAAA,IAChB;AAAA,IACA,SAAS;AACP,WAAK,SAAS;AAAA,IAChB;AAAA,EACF,CAAC,EAAE;AAGH,EAAM,gBAAU,MAAM;AACpB,QAAI,SAAS;AACX,UAASA,iBAAT,SAAuB,OAAmB;AACxC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,SAAS,MAAM;AACrB,YAAI,UAAU,SAAS,MAAM,GAAG;AAC9B,gCAAsB,UAAU;AAAA,QAClC,OAAO;AACL,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;AAAA,QACvD;AAAA,MACF,GAESC,kBAAT,SAAwB,OAAmB;AACzC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,gBAAgB,MAAM;AAY5B,YAAI,kBAAkB,KAAM;AAI5B,YAAI,CAAC,UAAU,SAAS,aAAa,GAAG;AACtC,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;AAAA,QACvD;AAAA,MACF,GAKSC,mBAAT,SAAyB,WAA6B;AACpD,cAAM,iBAAiB,SAAS;AAChC,YAAI,mBAAmB,SAAS,KAAM;AACtC,mBAAW,YAAY,WAAW;AAChC,cAAI,SAAS,aAAa,SAAS,EAAG,OAAM,SAAS;AAAA,QACvD;AAAA,MACF;AA1CS,0BAAAF,gBAUA,iBAAAC,iBA0BA,kBAAAC;AAQT,eAAS,iBAAiB,WAAWF,cAAa;AAClD,eAAS,iBAAiB,YAAYC,eAAc;AACpD,YAAM,mBAAmB,IAAI,iBAAiBC,gBAAe;AAC7D,UAAI,UAAW,kBAAiB,QAAQ,WAAW,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAErF,aAAO,MAAM;AACX,iBAAS,oBAAoB,WAAWF,cAAa;AACrD,iBAAS,oBAAoB,YAAYC,eAAc;AACvD,yBAAiB,WAAW;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,WAAW,WAAW,MAAM,CAAC;AAE1C,EAAM,gBAAU,MAAM;AACpB,QAAI,WAAW;AACb,uBAAiB,IAAI,UAAU;AAC/B,YAAM,2BAA2B,SAAS;AAC1C,YAAM,sBAAsB,UAAU,SAAS,wBAAwB;AAEvE,UAAI,CAAC,qBAAqB;AACxB,cAAM,aAAa,IAAI,YAAY,oBAAoB,aAAa;AACpE,kBAAU,iBAAiB,oBAAoB,gBAAgB;AAC/D,kBAAU,cAAc,UAAU;AAClC,YAAI,CAAC,WAAW,kBAAkB;AAChC,qBAAW,YAAY,sBAAsB,SAAS,CAAC,GAAG,EAAE,QAAQ,KAAK,CAAC;AAC1E,cAAI,SAAS,kBAAkB,0BAA0B;AACvD,kBAAM,SAAS;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAEA,aAAO,MAAM;AACX,kBAAU,oBAAoB,oBAAoB,gBAAgB;AAKlE,mBAAW,MAAM;AACf,gBAAM,eAAe,IAAI,YAAY,sBAAsB,aAAa;AACxE,oBAAU,iBAAiB,sBAAsB,kBAAkB;AACnE,oBAAU,cAAc,YAAY;AACpC,cAAI,CAAC,aAAa,kBAAkB;AAClC,kBAAM,4BAA4B,SAAS,MAAM,EAAE,QAAQ,KAAK,CAAC;AAAA,UACnE;AAEA,oBAAU,oBAAoB,sBAAsB,kBAAkB;AAEtE,2BAAiB,OAAO,UAAU;AAAA,QACpC,GAAG,CAAC;AAAA,MACN;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,kBAAkB,oBAAoB,UAAU,CAAC;AAGhE,QAAM,gBAAsB;AAAA,IAC1B,CAAC,UAA+B;AAC9B,UAAI,CAAC,QAAQ,CAAC,QAAS;AACvB,UAAI,WAAW,OAAQ;AAEvB,YAAM,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,WAAW,CAAC,MAAM;AAClF,YAAM,iBAAiB,SAAS;AAEhC,UAAI,YAAY,gBAAgB;AAC9B,cAAME,aAAY,MAAM;AACxB,cAAM,CAAC,OAAO,IAAI,IAAI,iBAAiBA,UAAS;AAChD,cAAM,4BAA4B,SAAS;AAG3C,YAAI,CAAC,2BAA2B;AAC9B,cAAI,mBAAmBA,WAAW,OAAM,eAAe;AAAA,QACzD,OAAO;AACL,cAAI,CAAC,MAAM,YAAY,mBAAmB,MAAM;AAC9C,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,UACzC,WAAW,MAAM,YAAY,mBAAmB,OAAO;AACrD,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,MAAM,EAAE,QAAQ,KAAK,CAAC;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,CAAC,MAAM,SAAS,WAAW,MAAM;AAAA,EACnC;AAEA,SACE,oBAAC,UAAU,KAAV,EAAc,UAAU,IAAK,GAAG,YAAY,KAAK,cAAc,WAAW,eAAe;AAE9F,CAAC;AAED,WAAW,cAAc;AAUzB,SAAS,WAAW,YAA2B,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AACtE,QAAM,2BAA2B,SAAS;AAC1C,aAAW,aAAa,YAAY;AAClC,UAAM,WAAW,EAAE,OAAO,CAAC;AAC3B,QAAI,SAAS,kBAAkB,yBAA0B;AAAA,EAC3D;AACF;AAKA,SAAS,iBAAiB,WAAwB;AAChD,QAAM,aAAa,sBAAsB,SAAS;AAClD,QAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,QAAM,OAAO,YAAY,WAAW,QAAQ,GAAG,SAAS;AACxD,SAAO,CAAC,OAAO,IAAI;AACrB;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;AAAA,IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;AAAA,IACpE;AAAA,EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAMA,SAAS,YAAY,UAAyB,WAAwB;AACpE,aAAW,WAAW,UAAU;AAE9B,QAAI,CAAC,SAAS,SAAS,EAAE,MAAM,UAAU,CAAC,EAAG,QAAO;AAAA,EACtD;AACF;AAEA,SAAS,SAAS,MAAmB,EAAE,KAAK,GAA2B;AACrE,MAAI,iBAAiB,IAAI,EAAE,eAAe,SAAU,QAAO;AAC3D,SAAO,MAAM;AAEX,QAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,QAAI,iBAAiB,IAAI,EAAE,YAAY,OAAQ,QAAO;AACtD,WAAO,KAAK;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,SAAmE;AAC5F,SAAO,mBAAmB,oBAAoB,YAAY;AAC5D;AAEA,SAAS,MAAM,SAAkC,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AAExE,MAAI,WAAW,QAAQ,OAAO;AAC5B,UAAM,2BAA2B,SAAS;AAE1C,YAAQ,MAAM,EAAE,eAAe,KAAK,CAAC;AAErC,QAAI,YAAY,4BAA4B,kBAAkB,OAAO,KAAK;AACxE,cAAQ,OAAO;AAAA,EACnB;AACF;AAOA,IAAM,mBAAmB,uBAAuB;AAEhD,SAAS,yBAAyB;AAEhC,MAAI,QAAyB,CAAC;AAE9B,SAAO;AAAA,IACL,IAAI,YAA2B;AAE7B,YAAM,mBAAmB,MAAM,CAAC;AAChC,UAAI,eAAe,kBAAkB;AACnC,0BAAkB,MAAM;AAAA,MAC1B;AAEA,cAAQ,YAAY,OAAO,UAAU;AACrC,YAAM,QAAQ,UAAU;AAAA,IAC1B;AAAA,IAEA,OAAO,YAA2B;AAChC,cAAQ,YAAY,OAAO,UAAU;AACrC,YAAM,CAAC,GAAG,OAAO;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,YAAe,OAAY,MAAS;AAC3C,QAAM,eAAe,CAAC,GAAG,KAAK;AAC9B,QAAM,QAAQ,aAAa,QAAQ,IAAI;AACvC,MAAI,UAAU,IAAI;AAChB,iBAAa,OAAO,OAAO,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAsB;AACzC,SAAO,MAAM,OAAO,CAAC,SAAS,KAAK,YAAY,GAAG;AACpD;AAEA,IAAM,OAAO;", "names": ["handleFocusIn", "handleFocusOut", "handleMutations", "container"]}