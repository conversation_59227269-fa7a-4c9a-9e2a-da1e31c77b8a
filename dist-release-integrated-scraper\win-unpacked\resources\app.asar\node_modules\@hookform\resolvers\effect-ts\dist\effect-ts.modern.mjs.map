{"version": 3, "file": "effect-ts.modern.mjs", "sources": ["../src/effect-ts.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { Effect } from 'effect';\n\nimport { ArrayFormatter, decodeUnknown } from 'effect/ParseResult';\nimport type { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nexport const effectTsResolver: Resolver =\n  (schema, config = { errors: 'all', onExcessProperty: 'ignore' }) =>\n  (values, _, options) => {\n    return decodeUnknown(\n      schema,\n      config,\n    )(values).pipe(\n      Effect.catchAll((parseIssue) =>\n        Effect.flip(ArrayFormatter.formatIssue(parseIssue)),\n      ),\n      Effect.mapError((issues) => {\n        const errors = issues.reduce((acc, current) => {\n          const key = current.path.join('.');\n          acc[key] = { message: current.message, type: current._tag };\n          return acc;\n        }, {} as FieldErrors);\n\n        return toNestErrors(errors, options);\n      }),\n      Effect.tap(() =>\n        Effect.sync(\n          () =>\n            options.shouldUseNativeValidation &&\n            validateFieldsNatively({}, options),\n        ),\n      ),\n      Effect.match({\n        onFailure: (errors) => ({ errors, values: {} }),\n        onSuccess: (result) => ({ errors: {}, values: result }),\n      }),\n      Effect.runPromise,\n    );\n  };\n"], "names": ["effectTsResolver", "schema", "config", "errors", "onExcessProperty", "values", "_", "options", "decodeUnknown", "pipe", "Effect", "catchAll", "parseIssue", "flip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatIssue", "mapError", "issues", "reduce", "acc", "current", "path", "join", "message", "type", "_tag", "toNestErrors", "tap", "sync", "shouldUseNativeValidation", "validateFieldsNatively", "match", "onFailure", "onSuccess", "result", "runPromise"], "mappings": "sLAOa,MAAAA,EACXA,CAACC,EAAQC,EAAS,CAAEC,OAAQ,MAAOC,iBAAkB,YACrD,CAACC,EAAQC,EAAGC,IACHC,EACLP,EACAC,EAFKM,CAGLH,GAAQI,KACRC,EAAOC,SAAUC,GACfF,EAAOG,KAAKC,EAAeC,YAAYH,KAEzCF,EAAOM,SAAUC,IACf,MAAMd,EAASc,EAAOC,OAAO,CAACC,EAAKC,KAEjCD,EADYC,EAAQC,KAAKC,KAAK,MACnB,CAAEC,QAASH,EAAQG,QAASC,KAAMJ,EAAQK,MAC9CN,GACN,CAAA,GAEH,OAAOO,EAAavB,EAAQI,EAAO,GAErCG,EAAOiB,IAAI,IACTjB,EAAOkB,KACL,IACErB,EAAQsB,2BACRC,EAAuB,CAAE,EAAEvB,KAGjCG,EAAOqB,MAAM,CACXC,UAAY7B,IAAY,CAAEA,SAAQE,OAAQ,KAC1C4B,UAAYC,IAAM,CAAQ/B,OAAQ,GAAIE,OAAQ6B,MAEhDxB,EAAOyB"}