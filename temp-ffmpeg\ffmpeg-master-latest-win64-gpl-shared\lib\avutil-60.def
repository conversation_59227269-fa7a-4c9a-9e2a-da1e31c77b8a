EXPORTS
    av_add_i
    av_add_q
    av_add_stable
    av_adler32_update
    av_aes_alloc
    av_aes_crypt
    av_aes_ctr_alloc
    av_aes_ctr_crypt
    av_aes_ctr_free
    av_aes_ctr_get_iv
    av_aes_ctr_increment_iv
    av_aes_ctr_init
    av_aes_ctr_set_full_iv
    av_aes_ctr_set_iv
    av_aes_ctr_set_random_iv
    av_aes_init
    av_aes_size
    av_ambient_viewing_environment_alloc
    av_ambient_viewing_environment_create_side_data
    av_amf_to_av_format
    av_append_path_component
    av_asprintf
    av_assert0_fpu
    av_audio_fifo_alloc
    av_audio_fifo_drain
    av_audio_fifo_free
    av_audio_fifo_peek
    av_audio_fifo_peek_at
    av_audio_fifo_read
    av_audio_fifo_realloc
    av_audio_fifo_reset
    av_audio_fifo_size
    av_audio_fifo_space
    av_audio_fifo_write
    av_av_to_amf_format
    av_base64_decode
    av_base64_encode
    av_basename
    av_bessel_i0
    av_blowfish_alloc
    av_blowfish_crypt
    av_blowfish_crypt_ecb
    av_blowfish_init
    av_bmg_get
    av_bprint_append_data
    av_bprint_chars
    av_bprint_clear
    av_bprint_escape
    av_bprint_finalize
    av_bprint_get_buffer
    av_bprint_init
    av_bprint_init_for_buffer
    av_bprint_strftime
    av_bprintf
    av_buffer_alloc
    av_buffer_allocz
    av_buffer_create
    av_buffer_default_free
    av_buffer_get_opaque
    av_buffer_get_ref_count
    av_buffer_is_writable
    av_buffer_make_writable
    av_buffer_pool_buffer_get_opaque
    av_buffer_pool_get
    av_buffer_pool_init
    av_buffer_pool_init2
    av_buffer_pool_uninit
    av_buffer_realloc
    av_buffer_ref
    av_buffer_replace
    av_buffer_unref
    av_calloc
    av_camellia_alloc
    av_camellia_crypt
    av_camellia_init
    av_camellia_size
    av_cast5_alloc
    av_cast5_crypt
    av_cast5_crypt2
    av_cast5_init
    av_cast5_size
    av_channel_description
    av_channel_description_bprint
    av_channel_from_string
    av_channel_layout_ambisonic_order
    av_channel_layout_channel_from_index
    av_channel_layout_channel_from_string
    av_channel_layout_check
    av_channel_layout_compare
    av_channel_layout_copy
    av_channel_layout_custom_init
    av_channel_layout_default
    av_channel_layout_describe
    av_channel_layout_describe_bprint
    av_channel_layout_from_mask
    av_channel_layout_from_string
    av_channel_layout_index_from_channel
    av_channel_layout_index_from_string
    av_channel_layout_retype
    av_channel_layout_standard
    av_channel_layout_subset
    av_channel_layout_uninit
    av_channel_name
    av_channel_name_bprint
    av_chroma_location_enum_to_pos
    av_chroma_location_from_name
    av_chroma_location_name
    av_chroma_location_pos_to_enum
    av_cmp_i
    av_color_primaries_from_name
    av_color_primaries_name
    av_color_range_from_name
    av_color_range_name
    av_color_space_from_name
    av_color_space_name
    av_color_transfer_from_name
    av_color_transfer_name
    av_compare_mod
    av_compare_ts
    av_container_fifo_alloc
    av_container_fifo_alloc_avframe
    av_container_fifo_can_read
    av_container_fifo_drain
    av_container_fifo_free
    av_container_fifo_peek
    av_container_fifo_read
    av_container_fifo_write
    av_content_light_metadata_alloc
    av_content_light_metadata_create_side_data
    av_cpu_count
    av_cpu_force_count
    av_cpu_max_align
    av_crc
    av_crc_get_table
    av_crc_init
    av_csp_approximate_trc_gamma
    av_csp_itu_eotf
    av_csp_itu_eotf_inv
    av_csp_luma_coeffs_from_avcsp
    av_csp_primaries_desc_from_id
    av_csp_primaries_id_from_desc
    av_csp_trc_func_from_id
    av_csp_trc_func_inv_from_id
    av_d2q
    av_default_get_category
    av_default_item_name
    av_des_alloc
    av_des_crypt
    av_des_init
    av_des_mac
    av_detection_bbox_alloc
    av_detection_bbox_create_side_data
    av_dict_copy
    av_dict_count
    av_dict_free
    av_dict_get
    av_dict_get_string
    av_dict_iterate
    av_dict_parse_string
    av_dict_set
    av_dict_set_int
    av_dirname
    av_display_matrix_flip
    av_display_rotation_get
    av_display_rotation_set
    av_div_i
    av_div_q
    av_dovi_alloc
    av_dovi_find_level
    av_dovi_metadata_alloc
    av_downmix_info_update_side_data
    av_dynamic_hdr_plus_alloc
    av_dynamic_hdr_plus_create_side_data
    av_dynamic_hdr_plus_from_t35
    av_dynamic_hdr_plus_to_t35
    av_dynamic_hdr_vivid_alloc
    av_dynamic_hdr_vivid_create_side_data
    av_dynarray2_add
    av_dynarray_add
    av_dynarray_add_nofree
    av_encryption_info_add_side_data
    av_encryption_info_alloc
    av_encryption_info_clone
    av_encryption_info_free
    av_encryption_info_get_side_data
    av_encryption_init_info_add_side_data
    av_encryption_init_info_alloc
    av_encryption_init_info_free
    av_encryption_init_info_get_side_data
    av_escape
    av_executor_alloc
    av_executor_execute
    av_executor_free
    av_expr_count_func
    av_expr_count_vars
    av_expr_eval
    av_expr_free
    av_expr_parse
    av_expr_parse_and_eval
    av_fast_malloc
    av_fast_mallocz
    av_fast_realloc
    av_fifo_alloc2
    av_fifo_auto_grow_limit
    av_fifo_can_read
    av_fifo_can_write
    av_fifo_drain2
    av_fifo_elem_size
    av_fifo_freep2
    av_fifo_grow2
    av_fifo_peek
    av_fifo_peek_to_cb
    av_fifo_read
    av_fifo_read_to_cb
    av_fifo_reset2
    av_fifo_write
    av_fifo_write_from_cb
    av_file_map
    av_file_unmap
    av_film_grain_params_alloc
    av_film_grain_params_create_side_data
    av_film_grain_params_select
    av_find_best_pix_fmt_of_2
    av_find_info_tag
    av_find_nearest_q_idx
    av_force_cpu_flags
    av_fourcc_make_string
    av_frame_alloc
    av_frame_apply_cropping
    av_frame_clone
    av_frame_copy
    av_frame_copy_props
    av_frame_free
    av_frame_get_buffer
    av_frame_get_plane_buffer
    av_frame_get_side_data
    av_frame_is_writable
    av_frame_make_writable
    av_frame_move_ref
    av_frame_new_side_data
    av_frame_new_side_data_from_buf
    av_frame_ref
    av_frame_remove_side_data
    av_frame_replace
    av_frame_side_data_add
    av_frame_side_data_clone
    av_frame_side_data_desc
    av_frame_side_data_free
    av_frame_side_data_get_c
    av_frame_side_data_name
    av_frame_side_data_new
    av_frame_side_data_remove
    av_frame_side_data_remove_by_props
    av_frame_unref
    av_free
    av_freep
    av_gcd
    av_gcd_q
    av_get_alt_sample_fmt
    av_get_bits_per_pixel
    av_get_bytes_per_sample
    av_get_cpu_flags
    av_get_known_color_name
    av_get_media_type_string
    av_get_packed_sample_fmt
    av_get_padded_bits_per_pixel
    av_get_picture_type_char
    av_get_pix_fmt
    av_get_pix_fmt_loss
    av_get_pix_fmt_name
    av_get_pix_fmt_string
    av_get_planar_sample_fmt
    av_get_random_seed
    av_get_sample_fmt
    av_get_sample_fmt_name
    av_get_sample_fmt_string
    av_get_time_base_q
    av_get_token
    av_gettime
    av_gettime_relative
    av_gettime_relative_is_monotonic
    av_hash_alloc
    av_hash_final
    av_hash_final_b64
    av_hash_final_bin
    av_hash_final_hex
    av_hash_freep
    av_hash_get_name
    av_hash_get_size
    av_hash_init
    av_hash_names
    av_hash_update
    av_hmac_alloc
    av_hmac_calc
    av_hmac_final
    av_hmac_free
    av_hmac_init
    av_hmac_update
    av_hwdevice_ctx_alloc
    av_hwdevice_ctx_create
    av_hwdevice_ctx_create_derived
    av_hwdevice_ctx_create_derived_opts
    av_hwdevice_ctx_init
    av_hwdevice_find_type_by_name
    av_hwdevice_get_hwframe_constraints
    av_hwdevice_get_type_name
    av_hwdevice_hwconfig_alloc
    av_hwdevice_iterate_types
    av_hwframe_constraints_free
    av_hwframe_ctx_alloc
    av_hwframe_ctx_create_derived
    av_hwframe_ctx_init
    av_hwframe_get_buffer
    av_hwframe_map
    av_hwframe_transfer_data
    av_hwframe_transfer_get_formats
    av_i2int
    av_iamf_audio_element_add_layer
    av_iamf_audio_element_alloc
    av_iamf_audio_element_free
    av_iamf_audio_element_get_class
    av_iamf_mix_presentation_add_submix
    av_iamf_mix_presentation_alloc
    av_iamf_mix_presentation_free
    av_iamf_mix_presentation_get_class
    av_iamf_param_definition_alloc
    av_iamf_param_definition_get_class
    av_iamf_submix_add_element
    av_iamf_submix_add_layout
    av_image_alloc
    av_image_check_sar
    av_image_check_size
    av_image_check_size2
    av_image_copy
    av_image_copy_plane
    av_image_copy_plane_uc_from
    av_image_copy_to_buffer
    av_image_copy_uc_from
    av_image_fill_arrays
    av_image_fill_black
    av_image_fill_color
    av_image_fill_linesizes
    av_image_fill_max_pixsteps
    av_image_fill_plane_sizes
    av_image_fill_pointers
    av_image_get_buffer_size
    av_image_get_linesize
    av_int2i
    av_int_list_length_for_size
    av_lfg_init
    av_lfg_init_from_data
    av_log
    av_log2
    av_log2_16bit
    av_log2_i
    av_log_default_callback
    av_log_format_line
    av_log_format_line2
    av_log_get_flags
    av_log_get_level
    av_log_once
    av_log_set_callback
    av_log_set_flags
    av_log_set_level
    av_lzo1x_decode
    av_malloc
    av_malloc_array
    av_mallocz
    av_mastering_display_metadata_alloc
    av_mastering_display_metadata_alloc_size
    av_mastering_display_metadata_create_side_data
    av_match_list
    av_match_name
    av_max_alloc
    av_md5_alloc
    av_md5_final
    av_md5_init
    av_md5_size
    av_md5_sum
    av_md5_update
    av_memcpy_backptr
    av_memdup
    av_mod_i
    av_mul_i
    av_mul_q
    av_murmur3_alloc
    av_murmur3_final
    av_murmur3_init
    av_murmur3_init_seeded
    av_murmur3_update
    av_nearer_q
    av_opt_child_class_iterate
    av_opt_child_next
    av_opt_copy
    av_opt_eval_double
    av_opt_eval_flags
    av_opt_eval_float
    av_opt_eval_int
    av_opt_eval_int64
    av_opt_eval_q
    av_opt_eval_uint
    av_opt_find
    av_opt_find2
    av_opt_flag_is_set
    av_opt_free
    av_opt_freep_ranges
    av_opt_get
    av_opt_get_array
    av_opt_get_array_size
    av_opt_get_chlayout
    av_opt_get_dict_val
    av_opt_get_double
    av_opt_get_image_size
    av_opt_get_int
    av_opt_get_key_value
    av_opt_get_pixel_fmt
    av_opt_get_q
    av_opt_get_sample_fmt
    av_opt_get_video_rate
    av_opt_is_set_to_default
    av_opt_is_set_to_default_by_name
    av_opt_next
    av_opt_ptr
    av_opt_query_ranges
    av_opt_query_ranges_default
    av_opt_serialize
    av_opt_set
    av_opt_set_array
    av_opt_set_bin
    av_opt_set_chlayout
    av_opt_set_defaults
    av_opt_set_defaults2
    av_opt_set_dict
    av_opt_set_dict2
    av_opt_set_dict_val
    av_opt_set_double
    av_opt_set_from_string
    av_opt_set_image_size
    av_opt_set_int
    av_opt_set_pixel_fmt
    av_opt_set_q
    av_opt_set_sample_fmt
    av_opt_set_video_rate
    av_opt_show2
    av_parse_color
    av_parse_cpu_caps
    av_parse_ratio
    av_parse_time
    av_parse_video_rate
    av_parse_video_size
    av_pix_fmt_count_planes
    av_pix_fmt_desc_get
    av_pix_fmt_desc_get_id
    av_pix_fmt_desc_next
    av_pix_fmt_get_chroma_sub_sample
    av_pix_fmt_swap_endianness
    av_pixelutils_get_sad_fn
    av_q2intfloat
    av_random_bytes
    av_rc4_alloc
    av_rc4_crypt
    av_rc4_init
    av_read_image_line
    av_read_image_line2
    av_realloc
    av_realloc_array
    av_realloc_f
    av_reallocp
    av_reallocp_array
    av_reduce
    av_refstruct_alloc_ext_c
    av_refstruct_exclusive
    av_refstruct_pool_alloc
    av_refstruct_pool_alloc_ext_c
    av_refstruct_pool_get
    av_refstruct_ref
    av_refstruct_ref_c
    av_refstruct_replace
    av_refstruct_unref
    av_rescale
    av_rescale_delta
    av_rescale_q
    av_rescale_q_rnd
    av_rescale_rnd
    av_ripemd_alloc
    av_ripemd_final
    av_ripemd_init
    av_ripemd_size
    av_ripemd_update
    av_sample_fmt_is_planar
    av_samples_alloc
    av_samples_alloc_array_and_samples
    av_samples_copy
    av_samples_fill_arrays
    av_samples_get_buffer_size
    av_samples_set_silence
    av_set_options_string
    av_sha512_alloc
    av_sha512_final
    av_sha512_init
    av_sha512_size
    av_sha512_update
    av_sha_alloc
    av_sha_final
    av_sha_init
    av_sha_size
    av_sha_update
    av_shr_i
    av_size_mult
    av_small_strptime
    av_spherical_alloc
    av_spherical_from_name
    av_spherical_projection_name
    av_spherical_tile_bounds
    av_sscanf
    av_stereo3d_alloc
    av_stereo3d_alloc_size
    av_stereo3d_create_side_data
    av_stereo3d_from_name
    av_stereo3d_primary_eye_from_name
    av_stereo3d_primary_eye_name
    av_stereo3d_type_name
    av_stereo3d_view_from_name
    av_stereo3d_view_name
    av_strcasecmp
    av_strdup
    av_strerror
    av_strireplace
    av_stristart
    av_stristr
    av_strlcat
    av_strlcatf
    av_strlcpy
    av_strncasecmp
    av_strndup
    av_strnstr
    av_strstart
    av_strtod
    av_strtok
    av_sub_i
    av_sub_q
    av_tdrdi_alloc
    av_tea_alloc
    av_tea_crypt
    av_tea_init
    av_tea_size
    av_thread_message_flush
    av_thread_message_queue_alloc
    av_thread_message_queue_free
    av_thread_message_queue_nb_elems
    av_thread_message_queue_recv
    av_thread_message_queue_send
    av_thread_message_queue_set_err_recv
    av_thread_message_queue_set_err_send
    av_thread_message_queue_set_free_func
    av_timecode_adjust_ntsc_framenum2
    av_timecode_check_frame_rate
    av_timecode_get_smpte
    av_timecode_get_smpte_from_framenum
    av_timecode_init
    av_timecode_init_from_components
    av_timecode_init_from_string
    av_timecode_make_mpeg_tc_string
    av_timecode_make_smpte_tc_string
    av_timecode_make_smpte_tc_string2
    av_timecode_make_string
    av_timegm
    av_tree_destroy
    av_tree_enumerate
    av_tree_find
    av_tree_insert
    av_tree_node_alloc
    av_tree_node_size
    av_ts_make_time_string2
    av_twofish_alloc
    av_twofish_crypt
    av_twofish_init
    av_twofish_size
    av_tx_init
    av_tx_uninit
    av_usleep
    av_utf8_decode
    av_util_ffversion
    av_uuid_parse
    av_uuid_parse_range
    av_uuid_unparse
    av_uuid_urn_parse
    av_vbprintf
    av_version_info
    av_video_enc_params_alloc
    av_video_enc_params_create_side_data
    av_video_hint_alloc
    av_video_hint_create_side_data
    av_vk_frame_alloc
    av_vkfmt_from_pixfmt
    av_vlog
    av_write_image_line
    av_write_image_line2
    av_xtea_alloc
    av_xtea_crypt
    av_xtea_init
    av_xtea_le_crypt
    av_xtea_le_init
    avpriv_alloc_fixed_dsp
    avpriv_cga_font
    avpriv_float_dsp_alloc
    avpriv_fopen_utf8
    avpriv_init_lls
    avpriv_open
    avpriv_report_missing_feature
    avpriv_request_sample
    avpriv_set_systematic_pal2
    avpriv_slicethread_create
    avpriv_slicethread_execute
    avpriv_slicethread_free
    avpriv_solve_lls
    avpriv_tempfile
    avpriv_vga16_font
    avutil_configuration
    avutil_license
    avutil_version
