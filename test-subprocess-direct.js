// 直接测试SubprocessManager功能
const { SubprocessManager } = require('./dist/main/subprocess-manager.js');
const { app } = require('electron');

console.log('🧪 直接测试SubprocessManager');
console.log('=====================================');

// 模拟app.isPackaged为true来测试打包环境逻辑
Object.defineProperty(app, 'isPackaged', {
  get: () => true
});

// 模拟process.resourcesPath
process.resourcesPath = './dist-release-final-fix/win-unpacked/resources';

async function testSubprocessManager() {
  try {
    console.log('📦 初始化SubprocessManager...');
    const subprocessManager = SubprocessManager.getInstance();
    
    console.log('🔍 测试搜索功能...');
    const searchResult = await subprocessManager.startSearchScraper('Cyberpunk 2077');
    console.log('✅ 搜索结果:', searchResult);
    
    console.log('📥 测试详情抓取功能...');
    const detailResult = await subprocessManager.startDetailScraper('https://flingtrainer.com/trainer/cyberpunk-2077-trainer/');
    console.log('✅ 详情抓取结果:', detailResult);
    
    console.log('🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 等待Electron初始化
app.whenReady().then(() => {
  testSubprocessManager();
}).catch(error => {
  console.error('❌ Electron初始化失败:', error);
});
