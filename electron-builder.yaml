# Electron Builder 配置文件
# 用于打包游戏修改器盒子为独立的 Windows 可执行文件

appId: com.flingtrainer.gamemodifierbox
productName: 游戏修改器盒子
copyright: "Copyright © 2024 游戏修改器盒子"

# 构建目录配置
directories:
  output: dist-release-final-fix
  buildResources: assets

# 包含的文件
files:
  - "dist/**/*"
  - "package.json"
  - "!src/**/*"           # 排除源代码
  - "!*.md"               # 排除文档文件
  - "!*.config.*"         # 排除配置文件

# 额外资源文件 - 这些文件会被复制到应用程序资源目录
extraResources:
  # 爬虫脚本文件
  - from: "dist/scraper"
    to: "scraper"
    filter:
      - "**/*"

  # 内置Chromium浏览器
  - from: "chrome-browser"
    to: "chromium"
    filter:
      - "**/*"

# Windows 平台配置
win:
  icon: "assets/icon.ico"
  target:
    - target: "nsis"
      arch: ["x64"]
    - target: "portable"
      arch: ["x64"]
  requestedExecutionLevel: "asInvoker"
  artifactName: "${productName}-${version}-${arch}.${ext}"

# NSIS 安装程序配置
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: "游戏修改器盒子"
  installerIcon: "assets/icon.ico"
  uninstallerIcon: "assets/icon.ico"
  installerHeaderIcon: "assets/icon.ico"
  deleteAppDataOnUninstall: false
  runAfterFinish: true
  menuCategory: "游戏工具"

# 便携版配置
portable:
  artifactName: "游戏修改器盒子-便携版.exe"

# 应用程序元数据
extraMetadata:
  main: "dist/main/main.js"
  homepage: "https://flingtrainer.com"
  description: "一站式游戏修改器下载和管理工具"
  author:
    name: "游戏修改器盒子团队"
    email: "<EMAIL>"

# 发布配置
publish: null  # 不自动发布

# 压缩配置
compression: "maximum"

# 包含原生依赖
includeSubNodeModules: true

# 重建原生模块
npmRebuild: true

# 额外的 asar 配置
asar: true
asarUnpack:
  - "dist/scraper/**/*"
  - "node_modules/**/*"