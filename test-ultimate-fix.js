/**
 * 游戏修改器盒子 - 终极修复版本测试脚本
 * 测试打包后的应用是否能正常工作
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
  appPath: './dist-release-complete-fix/win-unpacked/游戏修改器盒子.exe',
  testTimeout: 30000, // 30秒超时
  tests: [
    {
      name: '应用启动测试',
      description: '验证应用能否正常启动'
    },
    {
      name: '翻译功能测试',
      description: '验证翻译功能是否正常工作'
    },
    {
      name: '搜索功能测试', 
      description: '验证搜索功能是否正常工作'
    },
    {
      name: '下载功能测试',
      description: '验证下载选项获取是否正常工作'
    }
  ]
};

// 颜色输出函数
const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`
};

// 日志函数
function log(level, message) {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = `[${timestamp}]`;
  
  switch(level) {
    case 'info':
      console.log(`${colors.blue(prefix)} ${message}`);
      break;
    case 'success':
      console.log(`${colors.green(prefix)} ✅ ${message}`);
      break;
    case 'error':
      console.log(`${colors.red(prefix)} ❌ ${message}`);
      break;
    case 'warning':
      console.log(`${colors.yellow(prefix)} ⚠️  ${message}`);
      break;
    case 'test':
      console.log(`${colors.cyan(prefix)} 🧪 ${message}`);
      break;
  }
}

// 检查文件是否存在
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

// 主测试函数
async function runTests() {
  console.log(colors.bold('\n🎯 游戏修改器盒子 - 终极修复版本测试\n'));
  
  // 检查应用文件是否存在
  log('info', '检查应用文件...');
  if (!checkFileExists(TEST_CONFIG.appPath)) {
    log('error', `应用文件不存在: ${TEST_CONFIG.appPath}`);
    log('error', '请先运行 npm run dist 构建应用');
    process.exit(1);
  }
  log('success', '应用文件存在');

  // 检查爬虫脚本是否正确打包
  log('info', '检查爬虫脚本打包...');
  const scraperDir = './dist-release-complete-fix/win-unpacked/resources/scraper';
  const requiredScripts = ['translator.js', 'search-scraper.js', 'detail-scraper.js'];
  
  for (const script of requiredScripts) {
    const scriptPath = path.join(scraperDir, script);
    if (!checkFileExists(scriptPath)) {
      log('error', `爬虫脚本缺失: ${script}`);
      process.exit(1);
    }
  }
  log('success', '所有爬虫脚本已正确打包');

  // 启动应用进行测试
  log('test', '启动应用进行功能测试...');
  
  const appProcess = spawn(TEST_CONFIG.appPath, [], {
    stdio: ['pipe', 'pipe', 'pipe'],
    detached: false
  });

  let testResults = {
    startup: false,
    translation: false,
    search: false,
    download: false
  };

  // 监听应用输出
  appProcess.stdout.on('data', (data) => {
    const output = data.toString();
    log('info', `应用输出: ${output.trim()}`);
    
    // 检查启动成功标志
    if (output.includes('主窗口已创建') || output.includes('应用启动完成')) {
      testResults.startup = true;
      log('success', '应用启动成功');
    }
    
    // 检查子进程管理器日志
    if (output.includes('[SubprocessManager]')) {
      log('info', `子进程管理器: ${output.trim()}`);
      
      if (output.includes('✅ 子进程启动成功')) {
        log('success', '子进程启动成功');
      }
      
      if (output.includes('翻译结果:')) {
        testResults.translation = true;
        log('success', '翻译功能正常');
      }
      
      if (output.includes('搜索结果:')) {
        testResults.search = true;
        log('success', '搜索功能正常');
      }
      
      if (output.includes('详情抓取结果:')) {
        testResults.download = true;
        log('success', '下载功能正常');
      }
    }
  });

  appProcess.stderr.on('data', (data) => {
    const error = data.toString();
    if (error.trim()) {
      log('warning', `应用错误: ${error.trim()}`);
    }
  });

  // 设置测试超时
  setTimeout(() => {
    log('warning', '测试超时，终止应用进程');
    appProcess.kill();
    
    // 输出测试结果
    printTestResults(testResults);
    process.exit(0);
  }, TEST_CONFIG.testTimeout);

  // 应用退出处理
  appProcess.on('close', (code) => {
    log('info', `应用进程退出，代码: ${code}`);
    printTestResults(testResults);
    process.exit(code);
  });

  // 给应用一些时间启动
  setTimeout(() => {
    log('test', '应用应该已经启动，请手动测试以下功能：');
    console.log(colors.yellow('\n📋 手动测试清单：'));
    console.log('1. 🔍 在搜索框输入"黑神话悟空"，检查翻译和搜索是否正常');
    console.log('2. 📥 点击任意修改器的"立即下载"按钮，检查下载选项是否显示');
    console.log('3. 🔧 打开开发者工具查看控制台，确认有详细的调试信息');
    console.log('4. ❌ 如果功能正常，请按 Ctrl+C 结束测试\n');
  }, 5000);
}

// 打印测试结果
function printTestResults(results) {
  console.log(colors.bold('\n📊 测试结果总结：\n'));
  
  const tests = [
    { name: '应用启动', key: 'startup', status: results.startup },
    { name: '翻译功能', key: 'translation', status: results.translation },
    { name: '搜索功能', key: 'search', status: results.search },
    { name: '下载功能', key: 'download', status: results.download }
  ];
  
  tests.forEach(test => {
    const status = test.status ? colors.green('✅ 通过') : colors.red('❌ 失败');
    console.log(`${test.name}: ${status}`);
  });
  
  const passedTests = tests.filter(t => t.status).length;
  const totalTests = tests.length;
  
  console.log(colors.bold(`\n总计: ${passedTests}/${totalTests} 项测试通过\n`));
  
  if (passedTests === totalTests) {
    console.log(colors.green('🎉 所有测试通过！修复成功！'));
  } else {
    console.log(colors.yellow('⚠️  部分测试失败，需要进一步检查'));
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log('error', `测试运行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests };
