var e=require("react-hook-form"),r=function(r,t,i){if(r&&"reportValidity"in r){var s=e.get(i,t);r.setCustomValidity(s&&s.message||""),r.reportValidity()}},t=function(e,t){var i=function(i){var s=t.fields[i];s&&s.ref&&"reportValidity"in s.ref?r(s.ref,i,e):s.refs&&s.refs.forEach(function(t){return r(t,i,e)})};for(var s in t.fields)i(s)},i=function(e,r){return e.some(function(e){return e.startsWith(r+".")})};exports.toNestErrors=function(r,s){s.shouldUseNativeValidation&&t(r,s);var a={};for(var n in r){var o=e.get(s.fields,n),f=Object.assign(r[n]||{},{ref:o&&o.ref});if(i(s.names||Object.keys(r),n)){var u=Object.assign({},e.get(a,n));e.set(u,"root",f),e.set(a,n,u)}else e.set(a,n,f)}return a},exports.validateFieldsNatively=t;
//# sourceMappingURL=resolvers.js.map
