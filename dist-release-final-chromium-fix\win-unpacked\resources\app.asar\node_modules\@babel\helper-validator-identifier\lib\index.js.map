{"version": 3, "names": ["_identifier", "require", "_keyword"], "sources": ["../src/index.ts"], "sourcesContent": ["export {\n  isIdentifierName,\n  isIdentifierChar,\n  isIdentifierStart,\n} from \"./identifier.ts\";\nexport {\n  isReservedWord,\n  isStrictBindOnlyReservedWord,\n  isStrictBindReservedWord,\n  isStrictReservedWord,\n  isKeyword,\n} from \"./keyword.ts\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA", "ignoreList": []}