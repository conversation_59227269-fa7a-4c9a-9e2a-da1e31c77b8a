{"name": "@hookform/resolvers/valibot", "amdName": "hookformResolversValibot", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: valibot", "main": "dist/valibot.js", "module": "dist/valibot.module.js", "umd:main": "dist/valibot.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"@hookform/resolvers": "^2.0.0", "react-hook-form": "^7.0.0", "valibot": "^1.0.0 || ^1.0.0-beta || ^1.0.0-rc"}}