{"version": 3, "file": "arktype.umd.js", "sources": ["../src/arktype.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { ArkErrors } from 'arktype';\nimport { FieldError, FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (e: ArkErrors): Record<string, FieldError> => {\n  // copy code to type to match FieldError shape\n  e.forEach((e) => Object.assign(e, { type: e.code }));\n  // need to cast here because TS doesn't understand we added the type field\n  return e.byPath as never;\n};\n\nexport const arktypeResolver: Resolver =\n  (schema, _schemaOptions, resolverOptions = {}) =>\n  (values, _, options) => {\n    const out = schema(values);\n\n    if (out instanceof ArkErrors) {\n      return {\n        values: {},\n        errors: toNestErrors(parseErrorSchema(out), options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      errors: {} as FieldErrors,\n      values: resolverOptions.raw ? values : out,\n    };\n  };\n"], "names": ["schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "e", "out", "ArkErrors", "errors", "toNestErrors", "for<PERSON>ach", "Object", "assign", "type", "code", "by<PERSON><PERSON>", "shouldUseNativeValidation", "validateFieldsNatively", "raw"], "mappings": "wXAaE,SAACA,EAAQC,EAAgBC,GACzB,YADyBA,IAAAA,IAAAA,EAAkB,CAAA,GAC3C,SAACC,EAAQC,EAAGC,GACV,IAVsBC,EAUhBC,EAAMP,EAAOG,GAEnB,OAAII,aAAeC,EAAAA,UACV,CACLL,OAAQ,CAAE,EACVM,OAAQC,EAAYA,cAfFJ,EAeoBC,EAb5CD,EAAEK,QAAQ,SAACL,UAAMM,OAAOC,OAAOP,EAAG,CAAEQ,KAAMR,EAAES,MAAO,GAE5CT,EAAEU,QAWyCX,KAIhDA,EAAQY,2BAA6BC,EAAsBA,uBAAC,CAAE,EAAEb,GAEzD,CACLI,OAAQ,CAAiB,EACzBN,OAAQD,EAAgBiB,IAAMhB,EAASI,GAE3C,CAAC"}